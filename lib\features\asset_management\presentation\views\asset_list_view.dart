import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/widgets/pagination_widget.dart';
import 'package:logestics/features/asset_management/presentation/controllers/asset_list_controller.dart';
import 'package:logestics/features/asset_management/presentation/views/asset_form_view.dart';
import 'package:logestics/features/asset_management/presentation/views/asset_export_dialog.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:logestics/features/home/<USER>/theme.dart';
import 'package:logestics/debug/firebase_debug_helper.dart';
import 'package:logestics/debug/asset_status_tester.dart';

class AssetListView extends StatefulWidget {
  const AssetListView({super.key});

  @override
  State<AssetListView> createState() => _AssetListViewState();
}

class _AssetListViewState extends State<AssetListView> {
  late ColorNotifier notifier;
  late AssetListController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<AssetListController>();
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of<ColorNotifier>(context, listen: true);
    var width = Get.width;

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      body: Column(
        children: [
          _buildHeader(width),
          const SizedBox(height: 16),
          _buildFilters(),
          const SizedBox(height: 16),
          _buildSummaryCards(),
          const SizedBox(height: 16),
          Expanded(child: _buildAssetTable(width)),
          _buildPagination(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewAsset,
        backgroundColor: const Color(0xFF0165FC),
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildHeader(double width) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Text(
            'Asset Management',
            style: TextStyle(
              color: notifier.text,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (width > 600) ...[
            Obx(() => controller.selectedAssets.isNotEmpty
                ? Row(
                    children: [
                      Text(
                        '${controller.selectedAssets.length} selected',
                        style: TextStyle(color: notifier.text),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: _deleteSelectedAssets,
                        icon: const Icon(Icons.delete, size: 16),
                        label: const Text('Delete Selected'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: controller.clearSelection,
                        child: const Text('Clear Selection'),
                      ),
                    ],
                  )
                : const SizedBox()),
            const SizedBox(width: 16),
            ElevatedButton.icon(
              onPressed: _exportAssets,
              icon: const Icon(Icons.download, size: 16),
              label: const Text('Export Assets'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: _runFirebaseDebug,
              icon: const Icon(Icons.bug_report, size: 16),
              label: const Text('Debug Firebase'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: () {
                controller.loadAssets();
              },
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('Manual Refresh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: _runAssetStatusTests,
              icon: const Icon(Icons.science, size: 16),
              label: const Text('Test Status'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        color: notifier.getBgColor,
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextField(
                      controller: controller.searchController,
                      decoration: InputDecoration(
                        hintText: 'Search assets...',
                        hintStyle:
                            TextStyle(color: notifier.text.withOpacity(0.6)),
                        prefixIcon: Icon(Icons.search, color: notifier.text),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: notifier.textFileColor,
                      ),
                      style: TextStyle(color: notifier.text),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Obx(() => DropdownButtonFormField<String>(
                          value: controller.selectedTypeFilter.value.isEmpty
                              ? null
                              : controller.selectedTypeFilter.value,
                          decoration: InputDecoration(
                            labelText: 'Type',
                            labelStyle: TextStyle(color: notifier.text),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: notifier.textFileColor,
                          ),
                          dropdownColor: notifier.getBgColor,
                          style: TextStyle(color: notifier.text),
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('All Types'),
                            ),
                            ...AssetType.allTypes
                                .map((type) => DropdownMenuItem<String>(
                                      value: type,
                                      child: Text(type),
                                    )),
                          ],
                          onChanged: controller.setTypeFilter,
                        )),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Obx(() => DropdownButtonFormField<String>(
                          value: controller.selectedStatusFilter.value.isEmpty
                              ? null
                              : controller.selectedStatusFilter.value,
                          decoration: InputDecoration(
                            labelText: 'Status',
                            labelStyle: TextStyle(color: notifier.text),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: notifier.textFileColor,
                          ),
                          dropdownColor: notifier.getBgColor,
                          style: TextStyle(color: notifier.text),
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('All Statuses'),
                            ),
                            ...AssetStatus.allStatuses
                                .map((status) => DropdownMenuItem<String>(
                                      value: status,
                                      child: Text(status),
                                    )),
                          ],
                          onChanged: controller.setStatusFilter,
                        )),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildDatePicker(
                      'Start Date',
                      controller.startDateFilter.value,
                      (date) => controller.setDateRangeFilter(
                          date, controller.endDateFilter.value),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDatePicker(
                      'End Date',
                      controller.endDateFilter.value,
                      (date) => controller.setDateRangeFilter(
                          controller.startDateFilter.value, date),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: controller.clearFilters,
                    child: const Text('Clear Filters'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDatePicker(
      String label, DateTime? value, Function(DateTime?) onChanged) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: value ?? DateTime.now(),
          firstDate: DateTime(2000),
          lastDate: DateTime.now(),
        );
        onChanged(date);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
          color: notifier.textFileColor,
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: notifier.text, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                value != null
                    ? '${value.day}/${value.month}/${value.year}'
                    : label,
                style: TextStyle(
                  color: value != null
                      ? notifier.text
                      : notifier.text.withOpacity(0.6),
                ),
              ),
            ),
            if (value != null)
              InkWell(
                onTap: () => onChanged(null),
                child: Icon(Icons.clear, color: notifier.text, size: 16),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx(() => Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Total Assets',
                  controller.filteredAssets.length.toString(),
                  Icons.inventory,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'Total Value',
                  'PKR ${controller.formatCurrency(controller.totalAssetsValue)}',
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'Purchase Cost',
                  'PKR ${controller.formatCurrency(controller.totalPurchaseCost)}',
                  Icons.shopping_cart,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'Depreciation',
                  'PKR ${controller.formatCurrency(controller.totalDepreciation)}',
                  Icons.trending_down,
                  Colors.red,
                ),
              ),
            ],
          )),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      color: notifier.getBgColor,
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: notifier.text.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                color: notifier.text,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssetTable(double width) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        color: notifier.getBgColor,
        elevation: 2,
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (controller.paginatedAssets.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 64,
                    color: notifier.text.withOpacity(0.3),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No assets found',
                    style: TextStyle(
                      color: notifier.text.withOpacity(0.6),
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Add your first asset to get started',
                    style: TextStyle(
                      color: notifier.text.withOpacity(0.4),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              headingRowColor: WidgetStateProperty.all(
                notifier.text.withValues(alpha: 0.1),
              ),
              columns: [
                DataColumn(
                  label: Checkbox(
                    value: controller.isSelectAllMode.value,
                    onChanged: (value) {
                      if (value == true) {
                        controller.selectAllAssets();
                      } else {
                        controller.clearSelection();
                      }
                    },
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Asset Name',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Type',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Status',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Purchase Date',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Purchase Cost',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Current Value',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Location',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Actions',
                    style: TextStyle(
                      color: notifier.text,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
              rows: controller.paginatedAssets.map((asset) {
                final isSelected = controller.selectedAssets.contains(asset.id);
                return DataRow(
                  selected: isSelected,
                  cells: [
                    DataCell(
                      Checkbox(
                        value: isSelected,
                        onChanged: (value) {
                          controller.toggleAssetSelection(asset.id);
                        },
                      ),
                    ),
                    DataCell(
                      InkWell(
                        onTap: () => _viewAssetDetails(asset),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              asset.name,
                              style: TextStyle(
                                color: const Color(0xFF0165FC),
                                fontWeight: FontWeight.w500,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                            if (asset.brand.isNotEmpty ||
                                asset.model.isNotEmpty)
                              Text(
                                '${asset.brand} ${asset.model}'.trim(),
                                style: TextStyle(
                                  color: notifier.text.withOpacity(0.6),
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    DataCell(
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            controller.getTypeIcon(asset.type),
                            size: 16,
                            color: notifier.text.withOpacity(0.7),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            asset.type,
                            style: TextStyle(color: notifier.text),
                          ),
                        ],
                      ),
                    ),
                    DataCell(
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: controller
                              .getStatusColor(asset.status)
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: controller.getStatusColor(asset.status),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          asset.status,
                          style: TextStyle(
                            color: controller.getStatusColor(asset.status),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    DataCell(
                      Text(
                        '${asset.purchaseDate.day}/${asset.purchaseDate.month}/${asset.purchaseDate.year}',
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                    DataCell(
                      Text(
                        'PKR ${controller.formatCurrency(asset.purchaseCost)}',
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                    DataCell(
                      Text(
                        'PKR ${controller.formatCurrency(asset.currentValue)}',
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                    DataCell(
                      Text(
                        asset.location.isNotEmpty ? asset.location : '-',
                        style: TextStyle(color: notifier.text),
                      ),
                    ),
                    DataCell(
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.visibility, size: 16),
                            onPressed: () => _viewAssetDetails(asset),
                            tooltip: 'View Details',
                          ),
                          IconButton(
                            icon: const Icon(Icons.edit, size: 16),
                            onPressed: () => _editAsset(asset),
                            tooltip: 'Edit Asset',
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete,
                                size: 16, color: Colors.red),
                            onPressed: () => _deleteAsset(asset),
                            tooltip: 'Delete Asset',
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildPagination() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Obx(() => PaginationWidget(
            currentPage: controller.currentPage.value,
            totalPages: controller.totalPages,
            itemsPerPage: controller.itemsPerPage.value,
            onPageChanged: controller.setCurrentPage,
            onItemsPerPageChanged: controller.setItemsPerPage,
          )),
    );
  }

  void _addNewAsset() async {
    await Get.to(() => const AssetFormView());
    // Refresh the list to ensure UI is updated
    controller.refreshAssets();
  }

  void _viewAssetDetails(AssetModel asset) {
    Get.toNamed('/asset-detail', arguments: asset.id);
  }

  void _editAsset(AssetModel asset) async {
    await Get.to(() => AssetFormView(asset: asset));
    // Refresh the list to ensure UI is updated
    controller.refreshAssets();
  }

  void _deleteAsset(AssetModel asset) {
    Get.dialog(
      AlertDialog(
        backgroundColor: notifier.getBgColor,
        title: Text('Delete Asset', style: TextStyle(color: notifier.text)),
        content: Text(
          'Are you sure you want to delete "${asset.name}"? This action cannot be undone.',
          style: TextStyle(color: notifier.text),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteAsset(asset.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _deleteSelectedAssets() {
    Get.dialog(
      AlertDialog(
        backgroundColor: notifier.getBgColor,
        title: Text('Delete Selected Assets',
            style: TextStyle(color: notifier.text)),
        content: Text(
          'Are you sure you want to delete ${controller.selectedAssets.length} selected assets? This action cannot be undone.',
          style: TextStyle(color: notifier.text),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteSelectedAssets();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child:
                const Text('Delete All', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _exportAssets() {
    showDialog(
      context: context,
      builder: (context) => AssetExportDialog(
        assetListController: controller,
      ),
    );
  }

  void _runFirebaseDebug() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        title: Text('Running Firebase Debug Tests'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Check console for detailed logs...'),
          ],
        ),
      ),
    );

    try {
      // Run all debug tests
      await FirebaseDebugHelper.runAllTests();

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show completion dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Debug Tests Complete'),
            content: const Text(
                'Check the browser console (F12) for detailed debug logs.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show error dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Debug Test Error'),
            content: Text('Error running debug tests: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  void _runAssetStatusTests() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        title: Text('Running Asset Status Tests'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Testing asset status changes and synchronization...'),
            SizedBox(height: 8),
            Text('Check console for detailed logs...'),
          ],
        ),
      ),
    );

    try {
      // Run asset status tests
      await AssetStatusTester.runAllAssetStatusTests();

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show completion dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Asset Status Tests Complete'),
            content: const Text(
                'Asset status tests completed. Check the browser console (F12) for detailed results and any issues found.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show error dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Asset Status Test Error'),
            content: Text('Error running asset status tests: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }
}
