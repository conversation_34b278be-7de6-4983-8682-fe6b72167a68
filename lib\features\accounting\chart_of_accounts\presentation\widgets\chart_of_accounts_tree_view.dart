import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../controllers/chart_of_accounts_controller.dart';

/// Represents a tree node for virtualized rendering
class TreeNode {
  final String id;
  final String title;
  final String subtitle;
  final int level;
  final bool isCategory;
  final bool isExpanded;
  final AccountCategory? category;
  final ChartOfAccountsModel? account;
  final bool hasChildren;

  TreeNode({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.level,
    required this.isCategory,
    required this.isExpanded,
    this.category,
    this.account,
    this.hasChildren = false,
  });
}

class ChartOfAccountsTreeView extends StatelessWidget {
  const ChartOfAccountsTreeView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ChartOfAccountsController>();

    return Obx(() {
      // Build flat list of tree nodes for virtualized rendering
      final treeNodes = _buildTreeNodes(controller);

      if (treeNodes.isEmpty) {
        return _buildEmptyState(context);
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: treeNodes.length,
        itemBuilder: (context, index) {
          final node = treeNodes[index];

          if (node.isCategory) {
            return _buildCategoryNode(context, controller, node);
          } else {
            return _buildAccountNode(context, controller, node);
          }
        },
      );
    });
  }

  /// Build flat list of tree nodes for efficient rendering
  List<TreeNode> _buildTreeNodes(ChartOfAccountsController controller) {
    final nodes = <TreeNode>[];
    final accountsByCategory = controller.accountsByCategory;

    for (final category in AccountCategory.values) {
      final categoryAccounts = accountsByCategory[category] ?? [];
      final isExpanded = controller.expandedCategories.contains(category);

      // Add category node
      nodes.add(TreeNode(
        id: 'category_${category.name}',
        title: category.displayName,
        subtitle: '${categoryAccounts.length} accounts',
        level: 0,
        isCategory: true,
        isExpanded: isExpanded,
        category: category,
        hasChildren: categoryAccounts.isNotEmpty,
      ));

      // Add account nodes if category is expanded
      if (isExpanded && categoryAccounts.isNotEmpty) {
        final parentAccounts =
            controller.getParentAccountsForCategory(category);
        for (final account in parentAccounts) {
          _addAccountNodes(nodes, controller, account, 1);
        }
      }
    }

    return nodes;
  }

  /// Recursively add account nodes and their children
  void _addAccountNodes(
    List<TreeNode> nodes,
    ChartOfAccountsController controller,
    ChartOfAccountsModel account,
    int level,
  ) {
    // Get child accounts
    final childAccounts = controller.paginatedAccounts
        .where((acc) => acc.parentAccountId == account.id)
        .toList();

    nodes.add(TreeNode(
      id: account.id,
      title: account.accountName,
      subtitle: '${account.accountNumber} • ${account.accountType.displayName}',
      level: level,
      isCategory: false,
      isExpanded: true, // Accounts are always expanded for now
      account: account,
      hasChildren: childAccounts.isNotEmpty,
    ));

    // Add child accounts
    for (final childAccount in childAccounts) {
      _addAccountNodes(nodes, controller, childAccount, level + 1);
    }
  }

  /// Build empty state when no accounts exist
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_tree_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Accounts Found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first account to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
        ],
      ),
    );
  }

  /// Build category node widget
  Widget _buildCategoryNode(
    BuildContext context,
    ChartOfAccountsController controller,
    TreeNode node,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => controller.toggleCategoryExpansion(node.category!),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: node.category!.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: node.category!.color,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  node.category!.icon,
                  size: 18,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      node.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    Text(
                      node.subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ),
              Icon(
                node.isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey[600],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build account node widget
  Widget _buildAccountNode(
    BuildContext context,
    ChartOfAccountsController controller,
    TreeNode node,
  ) {
    final account = node.account!;
    final indentWidth = (node.level - 1) * 24.0;

    return Container(
      margin: EdgeInsets.only(
        left: indentWidth,
        bottom: 4,
      ),
      child: Card(
        elevation: 1,
        child: ListTile(
          dense: true,
          leading: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: account.isActive ? Colors.green : Colors.grey,
              shape: BoxShape.circle,
            ),
          ),
          title: Text(
            node.title,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: account.isActive ? null : Colors.grey[600],
            ),
          ),
          subtitle: Text(
            node.subtitle,
            style: TextStyle(
              fontSize: 12,
              color: account.isActive ? Colors.grey[600] : Colors.grey[500],
            ),
          ),
          trailing: PopupMenuButton<String>(
            onSelected: (action) => _handleAccountAction(
              context,
              controller,
              account,
              action,
            ),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('Edit'),
                  dense: true,
                ),
              ),
              if (account.isActive)
                const PopupMenuItem(
                  value: 'deactivate',
                  child: ListTile(
                    leading: Icon(Icons.visibility_off),
                    title: Text('Deactivate'),
                    dense: true,
                  ),
                )
              else
                const PopupMenuItem(
                  value: 'reactivate',
                  child: ListTile(
                    leading: Icon(Icons.visibility),
                    title: Text('Reactivate'),
                    dense: true,
                  ),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('Delete', style: TextStyle(color: Colors.red)),
                  dense: true,
                ),
              ),
            ],
          ),
          onTap: () => controller.startEditing(account),
        ),
      ),
    );
  }

  Widget _buildAccountTile(
    BuildContext context,
    ChartOfAccountsController controller,
    ChartOfAccountsModel account,
    int level,
  ) {
    final childAccounts = controller.paginatedAccounts
        .where((acc) => acc.parentAccountId == account.id)
        .toList();

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(left: level * 24.0),
          child: ListTile(
            leading: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (level > 0) ...[
                  Container(
                    width: 2,
                    height: 24,
                    color: Colors.grey[300],
                    margin: const EdgeInsets.only(right: 8),
                  ),
                ],
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color:
                        account.isActive ? account.category.color : Colors.grey,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),
            title: Row(
              children: [
                Text(
                  account.accountNumber,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontFamily: 'monospace',
                      ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    account.accountName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: account.isActive ? null : Colors.grey,
                        ),
                  ),
                ),
                if (!account.isActive)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'INACTIVE',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(account.accountType.displayName),
                if (account.description != null &&
                    account.description!.isNotEmpty)
                  Text(
                    account.description!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                Text(
                  'Balance: \$${account.balance.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: account.balance >= 0
                            ? Colors.green[700]
                            : Colors.red[700],
                      ),
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (action) => _handleAccountAction(
                context,
                controller,
                account,
                action,
              ),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('Edit'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                if (account.isActive)
                  const PopupMenuItem(
                    value: 'deactivate',
                    child: ListTile(
                      leading: Icon(Icons.visibility_off),
                      title: Text('Deactivate'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  )
                else
                  const PopupMenuItem(
                    value: 'reactivate',
                    child: ListTile(
                      leading: Icon(Icons.visibility),
                      title: Text('Reactivate'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                if (childAccounts.isEmpty)
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title:
                          Text('Delete', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
              ],
            ),
            onTap: () => controller.startEditing(account),
          ),
        ),
        // Render child accounts recursively
        ...childAccounts.map((childAccount) =>
            _buildAccountTile(context, controller, childAccount, level + 1)),
      ],
    );
  }

  void _handleAccountAction(
    BuildContext context,
    ChartOfAccountsController controller,
    ChartOfAccountsModel account,
    String action,
  ) {
    switch (action) {
      case 'edit':
        controller.startEditing(account);
        break;
      case 'deactivate':
        _showConfirmationDialog(
          context,
          'Deactivate Account',
          'Are you sure you want to deactivate "${account.accountName}"?',
          () => controller.deactivateAccount(account),
        );
        break;
      case 'reactivate':
        controller.reactivateAccount(account);
        break;
      case 'delete':
        _showConfirmationDialog(
          context,
          'Delete Account',
          'Are you sure you want to permanently delete "${account.accountName}"? This action cannot be undone.',
          () => controller.deleteAccount(account),
          isDestructive: true,
        );
        break;
    }
  }

  void _showConfirmationDialog(
    BuildContext context,
    String title,
    String message,
    VoidCallback onConfirm, {
    bool isDestructive = false,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            style: isDestructive
                ? ElevatedButton.styleFrom(backgroundColor: Colors.red)
                : null,
            child: Text(isDestructive ? 'Delete' : 'Confirm'),
          ),
        ],
      ),
    );
  }
}
