import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:logestics/repositories/asset/asset_repository.dart';

class AssetListController extends GetxController
    with PaginationMixin, AutoRefreshMixin {
  final AssetRepository _assetRepository;

  AssetListController(this._assetRepository);

  // Observable variables - Explicitly typed to prevent casting issues
  final RxList<AssetModel> assets = <AssetModel>[].obs;
  final RxList<AssetModel> filteredAssets = <AssetModel>[].obs;
  final isLoading = true.obs; // Start with loading state
  final searchQuery = ''.obs;

  // Real-time subscription
  StreamSubscription<List<AssetModel>>? _assetsSubscription;

  // Search and filter controllers
  final searchController = TextEditingController();

  // Filter variables
  final selectedTypeFilter = ''.obs;
  final selectedStatusFilter = ''.obs;
  final selectedLocationFilter = ''.obs;
  final selectedDepartmentFilter = ''.obs;
  final startDateFilter = Rx<DateTime?>(null);
  final endDateFilter = Rx<DateTime?>(null);

  // Bulk selection
  final selectedAssets = <String>[].obs;
  final isSelectAllMode = false.obs;

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    itemsPerPage.value = 25; // Set default items per page to 25
    _setupRealTimeUpdates();
  }

  @override
  void onClose() {
    // AGGRESSIVELY cancel subscription
    _assetsSubscription?.cancel();
    _assetsSubscription = null;
    searchController.dispose();
    super.onClose();
  }

  @override
  Future<void> forceRefresh() async {
    await loadAssets();
  }

  @override
  Future<void> refreshData() async {
    await loadAssets();
  }

  /// Set up real-time asset updates
  void _setupRealTimeUpdates() {
    // Cancel existing subscription
    _assetsSubscription?.cancel();
    _assetsSubscription = null;

    log('AssetListController: Setting up real-time listener for assets');

    // Set initial loading state
    isLoading.value = true;

    // Set up real-time listener for assets
    _assetsSubscription = _assetRepository.listenToAssets().listen(
      (assetsList) {
        log('AssetListController: Real-time update received ${assetsList.length} assets');
        // Use value assignment to prevent type casting issues
        assets.value = List<AssetModel>.from(assetsList);
        _applyFilters();
        isLoading.value = false;
      },
      onError: (error) {
        log('AssetListController: Error in real-time assets stream: $error');
        // Fallback to manual fetch on error
        loadAssets();
      },
    );

    // Listen for changes in assets and update filtered list
    ever(assets, (_) => _applyFilters());
    ever(searchQuery, (_) => _applyFilters());
  }

  /// Load assets from repository (fallback method)
  Future<void> loadAssets() async {
    try {
      isLoading.value = true;
      log('Loading assets...');

      final result = await _assetRepository.getAssets();
      result.fold(
        (failure) {
          log('Error loading assets: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (assetsList) {
          log('Successfully loaded ${assetsList.length} assets');
          // Use value assignment instead of assignAll to prevent type casting issues
          assets.value = List<AssetModel>.from(assetsList);
          _applyFilters();
        },
      );
    } catch (e) {
      log('Error loading assets: $e');
      SnackbarUtils.showError('Error', 'Failed to load assets');
    } finally {
      isLoading.value = false;
    }
  }

  /// Manually refresh assets data
  Future<void> refreshAssets() async {
    log('AssetListController: Manual refresh requested');
    await loadAssets();
  }

  /// Handle search text changes
  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _applyFilters();
  }

  /// Apply all filters and search
  void _applyFilters() {
    log('AssetListController: _applyFilters() called with ${assets.length} total assets');
    var filtered = assets.toList();

    // Log all current filter values
    log('AssetListController: Current filters - Search: "${searchQuery.value}", Type: "${selectedTypeFilter.value}", Status: "${selectedStatusFilter.value}", Location: "${selectedLocationFilter.value}", Department: "${selectedDepartmentFilter.value}"');

    // Log all asset statuses for debugging
    for (var asset in assets) {
      log('AssetListController: Asset "${asset.name}" has status: "${asset.status}"');
    }

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      final beforeCount = filtered.length;
      filtered = filtered
          .where((asset) =>
              asset.name.toLowerCase().contains(query) ||
              asset.type.toLowerCase().contains(query) ||
              asset.registrationNumber.toLowerCase().contains(query) ||
              asset.serialNumber.toLowerCase().contains(query) ||
              asset.brand.toLowerCase().contains(query) ||
              asset.model.toLowerCase().contains(query))
          .toList();
      log('AssetListController: Search filter applied - Before: $beforeCount, After: ${filtered.length}');
    }

    // Apply type filter
    if (selectedTypeFilter.value.isNotEmpty) {
      final beforeCount = filtered.length;
      filtered = filtered
          .where((asset) => asset.type == selectedTypeFilter.value)
          .toList();
      log('AssetListController: Type filter applied - Before: $beforeCount, After: ${filtered.length}');
    }

    // Apply status filter
    if (selectedStatusFilter.value.isNotEmpty) {
      final beforeCount = filtered.length;
      filtered = filtered
          .where((asset) => asset.status == selectedStatusFilter.value)
          .toList();
      log('AssetListController: Status filter applied - Before: $beforeCount, After: ${filtered.length}');
    }

    // Apply location filter
    if (selectedLocationFilter.value.isNotEmpty) {
      final beforeCount = filtered.length;
      filtered = filtered
          .where((asset) => asset.location
              .toLowerCase()
              .contains(selectedLocationFilter.value.toLowerCase()))
          .toList();
      log('AssetListController: Location filter applied - Before: $beforeCount, After: ${filtered.length}');
    }

    // Apply department filter
    if (selectedDepartmentFilter.value.isNotEmpty) {
      final beforeCount = filtered.length;
      filtered = filtered
          .where((asset) => asset.department
              .toLowerCase()
              .contains(selectedDepartmentFilter.value.toLowerCase()))
          .toList();
      log('AssetListController: Department filter applied - Before: $beforeCount, After: ${filtered.length}');
    }

    // Apply date range filter
    if (startDateFilter.value != null) {
      final beforeCount = filtered.length;
      filtered = filtered
          .where((asset) =>
              asset.purchaseDate.isAfter(startDateFilter.value!) ||
              asset.purchaseDate.isAtSameMomentAs(startDateFilter.value!))
          .toList();
      log('AssetListController: Start date filter applied - Before: $beforeCount, After: ${filtered.length}');
    }

    if (endDateFilter.value != null) {
      final beforeCount = filtered.length;
      filtered = filtered
          .where((asset) =>
              asset.purchaseDate.isBefore(endDateFilter.value!) ||
              asset.purchaseDate.isAtSameMomentAs(endDateFilter.value!))
          .toList();
      log('AssetListController: End date filter applied - Before: $beforeCount, After: ${filtered.length}');
    }

    log('AssetListController: Final filtered assets count: ${filtered.length}');
    filteredAssets.value = filtered;
    setTotalItems(filtered.length);

    // Reset to first page when filters change
    if (currentPage.value > 1) {
      setCurrentPage(1);
    }
  }

  /// Get paginated assets for current page
  List<AssetModel> get paginatedAssets {
    return paginateList(filteredAssets);
  }

  /// Clear all filters
  void clearFilters() {
    searchController.clear();
    selectedTypeFilter.value = '';
    selectedStatusFilter.value = '';
    selectedLocationFilter.value = '';
    selectedDepartmentFilter.value = '';
    startDateFilter.value = null;
    endDateFilter.value = null;
    _applyFilters();
  }

  /// Set type filter
  void setTypeFilter(String? type) {
    selectedTypeFilter.value = type ?? '';
    _applyFilters();
  }

  /// Set status filter
  void setStatusFilter(String? status) {
    selectedStatusFilter.value = status ?? '';
    _applyFilters();
  }

  /// Set location filter
  void setLocationFilter(String location) {
    selectedLocationFilter.value = location;
    _applyFilters();
  }

  /// Set department filter
  void setDepartmentFilter(String department) {
    selectedDepartmentFilter.value = department;
    _applyFilters();
  }

  /// Set date range filter
  void setDateRangeFilter(DateTime? startDate, DateTime? endDate) {
    startDateFilter.value = startDate;
    endDateFilter.value = endDate;
    _applyFilters();
  }

  /// Toggle asset selection
  void toggleAssetSelection(String assetId) {
    if (selectedAssets.contains(assetId)) {
      selectedAssets.remove(assetId);
    } else {
      selectedAssets.add(assetId);
    }
  }

  /// Select all visible assets
  void selectAllAssets() {
    selectedAssets.clear();
    selectedAssets.addAll(paginatedAssets.map((asset) => asset.id));
    isSelectAllMode.value = true;
  }

  /// Clear all selections
  void clearSelection() {
    selectedAssets.clear();
    isSelectAllMode.value = false;
  }

  /// Delete selected assets
  Future<void> deleteSelectedAssets() async {
    if (selectedAssets.isEmpty) {
      SnackbarUtils.showWarning('Warning', 'No assets selected');
      return;
    }

    try {
      isLoading.value = true;
      int successCount = 0;
      int failureCount = 0;

      for (final assetId in selectedAssets.toList()) {
        final result = await _assetRepository.deleteAsset(assetId);
        result.fold(
          (failure) {
            log('Failed to delete asset $assetId: ${failure.message}');
            failureCount++;
          },
          (success) {
            log('Successfully deleted asset $assetId');
            successCount++;
          },
        );
      }

      // Show result message
      if (successCount > 0) {
        SnackbarUtils.showSuccess(
          'Success',
          'Successfully deleted $successCount asset(s)${failureCount > 0 ? ', failed to delete $failureCount asset(s)' : ''}',
        );
      } else {
        SnackbarUtils.showError('Error', 'Failed to delete selected assets');
      }

      // Clear selection and reload
      clearSelection();
      await loadAssets();
    } catch (e) {
      log('Error deleting selected assets: $e');
      SnackbarUtils.showError('Error', 'Failed to delete selected assets');
    } finally {
      isLoading.value = false;
    }
  }

  /// Delete single asset
  Future<void> deleteAsset(String assetId) async {
    try {
      isLoading.value = true;
      log('Deleting asset: $assetId');

      final result = await _assetRepository.deleteAsset(assetId);
      result.fold(
        (failure) {
          log('Error deleting asset: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (success) {
          log('Asset deleted successfully');
          SnackbarUtils.showSuccess('Success', 'Asset deleted successfully');
          loadAssets(); // Reload the list
        },
      );
    } catch (e) {
      log('Error deleting asset: $e');
      SnackbarUtils.showError('Error', 'Failed to delete asset');
    } finally {
      isLoading.value = false;
    }
  }

  /// Get unique locations from assets
  List<String> get uniqueLocations {
    final locations = assets
        .map((asset) => asset.location)
        .where((location) => location.isNotEmpty)
        .toSet()
        .toList();
    locations.sort();
    return locations;
  }

  /// Get unique departments from assets
  List<String> get uniqueDepartments {
    final departments = assets
        .map((asset) => asset.department)
        .where((department) => department.isNotEmpty)
        .toSet()
        .toList();
    departments.sort();
    return departments;
  }

  /// Get status color for UI
  Color getStatusColor(String status) {
    switch (status) {
      case 'In Use':
        return Colors.green;
      case 'Under Maintenance':
        return Colors.orange;
      case 'Retired':
        return Colors.grey;
      case 'Sold':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  /// Get type icon for UI
  IconData getTypeIcon(String type) {
    switch (type) {
      case 'Vehicle':
        return Icons.directions_car;
      case 'Machinery':
        return Icons.precision_manufacturing;
      case 'Tools':
        return Icons.build;
      case 'Equipment':
        return Icons.devices;
      default:
        return Icons.inventory;
    }
  }

  /// Format currency for display
  String formatCurrency(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }

  /// Calculate total value of all assets
  double get totalAssetsValue {
    return filteredAssets.fold(0.0, (sum, asset) => sum + asset.currentValue);
  }

  /// Calculate total purchase cost of all assets
  double get totalPurchaseCost {
    return filteredAssets.fold(0.0, (sum, asset) => sum + asset.purchaseCost);
  }

  /// Calculate total depreciation
  double get totalDepreciation {
    return totalPurchaseCost - totalAssetsValue;
  }
}
