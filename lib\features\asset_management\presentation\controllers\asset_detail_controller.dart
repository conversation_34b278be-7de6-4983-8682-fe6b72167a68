import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/models/asset/asset_model.dart';
import 'package:logestics/models/asset/asset_maintenance_model.dart';
import 'package:logestics/repositories/asset/asset_repository.dart';
import 'package:logestics/features/asset_management/presentation/views/asset_form_view.dart';
import 'package:logestics/features/asset_management/presentation/views/maintenance_form_dialog.dart';

class AssetDetailController extends GetxController {
  final AssetRepository _assetRepository = Get.find<AssetRepository>();

  // Observable variables
  final Rx<AssetModel?> asset = Rx<AssetModel?>(null);
  final RxList<AssetMaintenanceModel> maintenanceRecords =
      <AssetMaintenanceModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMaintenance = false.obs;
  final RxString error = ''.obs;
  final RxInt selectedTabIndex = 0.obs;

  // Asset ID
  String assetId = '';

  /// Initialize controller with asset ID
  void initializeWithAssetId(String id) {
    assetId = id;
    loadAssetDetails();
    loadMaintenanceHistory();
  }

  /// Load asset details
  Future<void> loadAssetDetails() async {
    if (assetId.isEmpty) return;

    try {
      isLoading.value = true;
      error.value = '';
      log('Loading asset details for ID: $assetId');

      final result = await _assetRepository.getAssetById(assetId);
      result.fold(
        (failure) {
          error.value = failure.message;
          log('Error loading asset details: ${failure.message}');
          SnackbarUtils.showError('Error', failure.message);
        },
        (assetData) {
          asset.value = assetData;
          log('Successfully loaded asset: ${assetData.name}');
        },
      );
    } catch (e) {
      error.value = 'Failed to load asset details';
      log('Error loading asset details: $e');
      SnackbarUtils.showError('Error', 'Failed to load asset details');
    } finally {
      isLoading.value = false;
    }
  }

  /// Load maintenance history
  Future<void> loadMaintenanceHistory() async {
    if (assetId.isEmpty) return;

    try {
      isLoadingMaintenance.value = true;
      log('Loading maintenance history for asset: $assetId');

      final result = await _assetRepository.getMaintenanceByAssetId(assetId);
      result.fold(
        (failure) {
          log('Error loading maintenance history: ${failure.message}');
          // Don't show error for maintenance history as it's not critical
        },
        (records) {
          maintenanceRecords.value = records;
          log('Successfully loaded ${records.length} maintenance records');
        },
      );
    } catch (e) {
      log('Error loading maintenance history: $e');
    } finally {
      isLoadingMaintenance.value = false;
    }
  }

  /// Refresh all data
  Future<void> refreshData() async {
    await Future.wait([
      loadAssetDetails(),
      loadMaintenanceHistory(),
    ]);
  }

  /// Navigate to edit asset
  void editAsset() {
    if (asset.value == null) return;

    Get.to(() => AssetFormView(asset: asset.value))?.then((result) {
      if (result == true) {
        // Real-time updates will automatically refresh the asset data
        // But we still need to refresh this specific asset details
        loadAssetDetails();
      }
    });
  }

  /// Add new maintenance record
  void addMaintenance() async {
    if (asset.value == null) return;

    final result = await Get.dialog<bool>(
      MaintenanceFormDialog(
        assetId: asset.value!.id,
        assetName: asset.value!.name,
      ),
    );

    if (result == true) {
      // Refresh maintenance history
      loadMaintenanceHistory();
    }
  }

  /// Edit maintenance record
  void editMaintenance(AssetMaintenanceModel maintenance) async {
    if (asset.value == null) return;

    final result = await Get.dialog<bool>(
      MaintenanceFormDialog(
        assetId: asset.value!.id,
        assetName: asset.value!.name,
        maintenance: maintenance,
      ),
    );

    if (result == true) {
      // Refresh maintenance history
      loadMaintenanceHistory();
    }
  }

  /// Change selected tab
  void changeTab(int index) {
    selectedTabIndex.value = index;
  }

  /// Get asset status color
  Color getStatusColor() {
    if (asset.value == null) return Colors.grey;

    switch (asset.value!.status) {
      case 'In Use':
        return Colors.green;
      case 'Under Maintenance':
        return Colors.orange;
      case 'Retired':
        return Colors.red;
      case 'Sold':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// Get depreciation percentage
  double getDepreciationPercentage() {
    if (asset.value == null || asset.value!.purchaseCost == 0) return 0.0;

    final depreciation = asset.value!.purchaseCost - asset.value!.currentValue;
    return (depreciation / asset.value!.purchaseCost) * 100;
  }

  /// Get total maintenance cost
  double getTotalMaintenanceCost() {
    return maintenanceRecords.fold(0.0, (sum, record) => sum + record.cost);
  }

  /// Get last maintenance date
  String getLastMaintenanceDate() {
    if (maintenanceRecords.isEmpty) return 'Never';

    // Records are sorted by date (descending)
    final lastMaintenance = maintenanceRecords.first;
    final now = DateTime.now();
    final difference = now.difference(lastMaintenance.maintenanceDate).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 30) {
      return '$difference days ago';
    } else if (difference < 365) {
      final months = (difference / 30).round();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference / 365).round();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }

  /// Format currency
  String formatCurrency(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }

  /// Get maintenance trend (increase/decrease in cost)
  double getMaintenanceTrend() {
    if (maintenanceRecords.length < 2) return 0.0;

    // Calculate trend based on last 6 months vs previous 6 months
    final now = DateTime.now();
    final sixMonthsAgo = DateTime(now.year, now.month - 6, now.day);
    final twelveMonthsAgo = DateTime(now.year, now.month - 12, now.day);

    final recentCost = maintenanceRecords
        .where((record) => record.maintenanceDate.isAfter(sixMonthsAgo))
        .fold(0.0, (sum, record) => sum + record.cost);

    final previousCost = maintenanceRecords
        .where((record) =>
            record.maintenanceDate.isAfter(twelveMonthsAgo) &&
            record.maintenanceDate.isBefore(sixMonthsAgo))
        .fold(0.0, (sum, record) => sum + record.cost);

    if (previousCost == 0) return recentCost > 0 ? 100.0 : 0.0;

    return ((recentCost - previousCost) / previousCost) * 100;
  }
}
