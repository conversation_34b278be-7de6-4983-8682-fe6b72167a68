import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import 'package:logestics/features/asset_management/presentation/controllers/asset_list_controller.dart';
import 'package:logestics/models/asset/asset_model.dart';

// Web-specific imports
import 'dart:html' as html show Blob, Url, AnchorElement, document;
// ignore: avoid_web_libraries_in_flutter

class AssetExportController extends GetxController {
  final AssetListController assetListController =
      Get.find<AssetListController>();

  // Date range filters
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Filter options
  final RxString selectedTypeFilter = ''.obs;
  final RxString selectedStatusFilter = ''.obs;
  final RxString selectedLocationFilter = ''.obs;
  final RxString selectedDepartmentFilter = ''.obs;

  // Loading state
  final RxBool isLoading = false.obs;

  // Filtered assets for export
  final RxList<AssetModel> filteredAssets = <AssetModel>[].obs;

  @override
  void onInit() {
    super.onInit();

    // Set default date range (all time)
    final now = DateTime.now();
    startDate.value = DateTime(2020, 1, 1); // Default start date
    endDate.value = now;

    // Listen to filter changes
    ever(startDate, (_) => _filterAssets());
    ever(endDate, (_) => _filterAssets());
    ever(selectedTypeFilter, (_) => _filterAssets());
    ever(selectedStatusFilter, (_) => _filterAssets());
    ever(selectedLocationFilter, (_) => _filterAssets());
    ever(selectedDepartmentFilter, (_) => _filterAssets());

    // Initial filter
    _filterAssets();
  }

  void _filterAssets() {
    List<AssetModel> assets = List.from(assetListController.assets);

    // Filter by date range
    if (startDate.value != null && endDate.value != null) {
      assets = assets.where((asset) {
        final purchaseDate = DateTime(
          asset.purchaseDate.year,
          asset.purchaseDate.month,
          asset.purchaseDate.day,
        );
        final start = DateTime(
          startDate.value!.year,
          startDate.value!.month,
          startDate.value!.day,
        );
        final end = DateTime(
          endDate.value!.year,
          endDate.value!.month,
          endDate.value!.day,
        );

        return purchaseDate.isAfter(start.subtract(const Duration(days: 1))) &&
            purchaseDate.isBefore(end.add(const Duration(days: 1)));
      }).toList();
    }

    // Filter by type
    if (selectedTypeFilter.value.isNotEmpty) {
      assets = assets
          .where((asset) => asset.type == selectedTypeFilter.value)
          .toList();
    }

    // Filter by status
    if (selectedStatusFilter.value.isNotEmpty) {
      assets = assets
          .where((asset) => asset.status == selectedStatusFilter.value)
          .toList();
    }

    // Filter by location
    if (selectedLocationFilter.value.isNotEmpty) {
      assets = assets
          .where((asset) => asset.location
              .toLowerCase()
              .contains(selectedLocationFilter.value.toLowerCase()))
          .toList();
    }

    // Filter by department
    if (selectedDepartmentFilter.value.isNotEmpty) {
      assets = assets
          .where((asset) => asset.department
              .toLowerCase()
              .contains(selectedDepartmentFilter.value.toLowerCase()))
          .toList();
    }

    filteredAssets.value = assets;
    log('Filtered ${filteredAssets.length} assets for export');
  }

  Future<void> selectStartDate() async {
    final date = await showDatePicker(
      context: Get.context!,
      initialDate: startDate.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: endDate.value ?? DateTime.now(),
    );
    if (date != null) {
      startDate.value = date;
    }
  }

  Future<void> selectEndDate() async {
    final date = await showDatePicker(
      context: Get.context!,
      initialDate: endDate.value ?? DateTime.now(),
      firstDate: startDate.value ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      endDate.value = date;
    }
  }

  void setTypeFilter(dynamic type) {
    selectedTypeFilter.value = type?.toString() ?? '';
  }

  void setStatusFilter(dynamic status) {
    selectedStatusFilter.value = status?.toString() ?? '';
  }

  void setLocationFilter(String location) {
    selectedLocationFilter.value = location;
  }

  void setDepartmentFilter(String department) {
    selectedDepartmentFilter.value = department;
  }

  void clearFilters() {
    selectedTypeFilter.value = '';
    selectedStatusFilter.value = '';
    selectedLocationFilter.value = '';
    selectedDepartmentFilter.value = '';
    final now = DateTime.now();
    startDate.value = DateTime(2020, 1, 1);
    endDate.value = now;
  }

  Future<void> generateExcel() async {
    if (filteredAssets.isEmpty) {
      Get.snackbar(
        'No Data',
        'No assets found for the selected filters',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    isLoading.value = true;

    try {
      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName =
          'Assets_Export_${dateFormat.format(DateTime.now())}.xlsx';

      log('Generating Excel file: $fileName with ${filteredAssets.length} assets');

      // Create Excel workbook
      final excel = Excel.createExcel();
      final sheet = excel['Assets Report'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      int currentRow = 0;

      // Title
      sheet
          .cell(
              CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('ASSET MANAGEMENT REPORT');
      sheet.merge(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
        CellIndex.indexByColumnRow(columnIndex: 11, rowIndex: currentRow),
      );
      currentRow += 2;

      // Report info
      sheet
              .cell(CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value =
          TextCellValue(
              'Generated on: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}');
      currentRow++;

      if (startDate.value != null && endDate.value != null) {
        sheet
                .cell(CellIndex.indexByColumnRow(
                    columnIndex: 0, rowIndex: currentRow))
                .value =
            TextCellValue(
                'Date Range: ${DateFormat('dd/MM/yyyy').format(startDate.value!)} - ${DateFormat('dd/MM/yyyy').format(endDate.value!)}');
        currentRow++;
      }

      sheet
          .cell(
              CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('Total Assets: ${filteredAssets.length}');
      currentRow += 2;

      // Headers
      final headers = [
        'S.No.',
        'Asset Name',
        'Type',
        'Brand',
        'Model',
        'Registration No.',
        'Serial No.',
        'Status',
        'Purchase Date',
        'Purchase Cost (PKR)',
        'Current Value (PKR)',
        'Depreciation (PKR)',
        'Location',
        'Department',
        'Vendor',
        'Supplier',
        'Useful Life (Years)',
        'Depreciation Method',
        'Notes'
      ];

      for (int i = 0; i < headers.length; i++) {
        sheet
            .cell(CellIndex.indexByColumnRow(
                columnIndex: i, rowIndex: currentRow))
            .value = TextCellValue(headers[i]);
      }
      currentRow++;

      // Data rows
      for (int i = 0; i < filteredAssets.length; i++) {
        final asset = filteredAssets[i];
        final depreciation = asset.purchaseCost - asset.currentValue;

        final rowData = [
          (i + 1).toString(),
          asset.name,
          asset.type,
          asset.brand,
          asset.model,
          asset.registrationNumber,
          asset.serialNumber,
          asset.status,
          DateFormat('dd/MM/yyyy').format(asset.purchaseDate),
          asset.purchaseCost.toStringAsFixed(2),
          asset.currentValue.toStringAsFixed(2),
          depreciation.toStringAsFixed(2),
          asset.location,
          asset.department,
          asset.vendor,
          asset.supplier,
          asset.estimatedUsefulLife.toString(),
          asset.depreciationMethod,
          asset.notes,
        ];

        for (int j = 0; j < rowData.length; j++) {
          sheet
              .cell(CellIndex.indexByColumnRow(
                  columnIndex: j, rowIndex: currentRow))
              .value = TextCellValue(rowData[j]);
        }
        currentRow++;
      }

      currentRow += 2;

      // Summary section
      sheet
          .cell(
              CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('SUMMARY');
      currentRow++;

      final totalPurchaseCost =
          filteredAssets.fold(0.0, (sum, asset) => sum + asset.purchaseCost);
      final totalCurrentValue =
          filteredAssets.fold(0.0, (sum, asset) => sum + asset.currentValue);
      final totalDepreciation = totalPurchaseCost - totalCurrentValue;

      sheet
              .cell(CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value =
          TextCellValue(
              'Total Purchase Cost: PKR ${totalPurchaseCost.toStringAsFixed(2)}');
      currentRow++;

      sheet
              .cell(CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value =
          TextCellValue(
              'Total Current Value: PKR ${totalCurrentValue.toStringAsFixed(2)}');
      currentRow++;

      sheet
              .cell(CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value =
          TextCellValue(
              'Total Depreciation: PKR ${totalDepreciation.toStringAsFixed(2)}');
      currentRow++;

      // Asset type breakdown
      final typeBreakdown = <String, int>{};
      for (final asset in filteredAssets) {
        typeBreakdown[asset.type] = (typeBreakdown[asset.type] ?? 0) + 1;
      }

      currentRow++;
      sheet
          .cell(
              CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          .value = TextCellValue('ASSET TYPE BREAKDOWN');
      currentRow++;

      for (final entry in typeBreakdown.entries) {
        sheet
            .cell(CellIndex.indexByColumnRow(
                columnIndex: 0, rowIndex: currentRow))
            .value = TextCellValue('${entry.key}: ${entry.value} assets');
        currentRow++;
      }

      // Generate Excel bytes
      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw Exception('Failed to generate Excel file');
      }

      // Download file for web
      if (kIsWeb) {
        await _downloadExcelWeb(Uint8List.fromList(excelBytes), fileName);
      } else {
        // For mobile/desktop, you would save to file system
        throw UnimplementedError(
            'Mobile/Desktop Excel download not implemented');
      }

      log('Excel file generated successfully: $fileName');

      // Close the dialog after successful generation
      Get.back();

      // Show success message
      Get.snackbar(
        'Success',
        'Excel file "$fileName" has been downloaded',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Error generating Excel file: $e');

      // Show error message
      Get.snackbar(
        'Error',
        'Failed to generate Excel file: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Download Excel file for web platform
  Future<void> _downloadExcelWeb(Uint8List excelBytes, String fileName) async {
    try {
      // Create blob with proper MIME type for Excel files
      final blob = html.Blob([excelBytes],
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      final url = html.Url.createObjectUrlFromBlob(blob);

      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', fileName)
        ..style.display = 'none';

      // Add to document, click, and remove
      html.document.body?.append(anchor);
      anchor.click();
      anchor.remove();

      html.Url.revokeObjectUrl(url);

      log('Excel file downloaded successfully on web: $fileName');
    } catch (e) {
      log('Error downloading Excel file on web: $e');
      rethrow;
    }
  }

  String formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00');
    return formatter.format(amount);
  }

  String formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }
}
