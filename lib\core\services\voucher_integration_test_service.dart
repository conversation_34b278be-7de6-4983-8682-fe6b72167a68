import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';

import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/voucher_model.dart';
import 'voucher_accounting_hook_service.dart';
import 'account_ledger_service.dart';

/// Service for testing the complete voucher-to-journal entry integration
class VoucherIntegrationTestService {
  static VoucherIntegrationTestService? _instance;
  final ChartOfAccountsFirebaseService _accountsService =
      ChartOfAccountsFirebaseService();
  final JournalEntryFirebaseService _journalService =
      JournalEntryFirebaseService();
  final GeneralLedgerFirebaseService _ledgerService =
      GeneralLedgerFirebaseService();
  final AccountLedgerService _accountLedgerService = AccountLedgerService();
  final VoucherAccountingHookService _hookService =
      VoucherAccountingHookService();

  VoucherIntegrationTestService._internal();

  factory VoucherIntegrationTestService() {
    _instance ??= VoucherIntegrationTestService._internal();
    return _instance!;
  }

  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'test-user';

  /// Run comprehensive integration test
  Future<IntegrationTestResult> runComprehensiveTest() async {
    try {
      log('Starting comprehensive voucher-to-journal integration test');

      final result = IntegrationTestResult();

      // Step 1: Create test Chart of Accounts
      log('Step 1: Creating test Chart of Accounts');
      final accounts = await _createTestAccounts();
      result.testAccounts = accounts;
      result.addStep('Created ${accounts.length} test accounts', true);

      // Step 2: Create test voucher
      log('Step 2: Creating test voucher');
      final voucher = _createTestVoucher(accounts);
      result.testVoucher = voucher;
      result.addStep('Created test voucher: ${voucher.voucherNumber}', true);

      // Step 3: Trigger voucher accounting hook
      log('Step 3: Triggering voucher accounting hook');
      await _hookService.onVoucherCreatedFromModel(voucher, _uid);
      result.addStep('Triggered voucher accounting hook', true);

      // Wait a moment for async operations
      await Future.delayed(const Duration(seconds: 2));

      // Step 4: Verify journal entries were created
      log('Step 4: Verifying journal entries');
      final journalEntries = await _verifyJournalEntries(voucher.voucherNumber);
      result.journalEntries = journalEntries;
      result.addStep('Found ${journalEntries.length} journal entries',
          journalEntries.isNotEmpty);

      // Step 5: Verify account balances were updated
      log('Step 5: Verifying account balances');
      final balanceResults = await _verifyAccountBalances(accounts);
      result.accountBalances = balanceResults;
      result.addStep('Verified account balances', balanceResults.isNotEmpty);

      // Step 6: Verify account ledger entries were created
      log('Step 6: Verifying account ledger entries');
      final ledgerResults = await _verifyAccountLedgerEntries(accounts);
      result.ledgerEntries = ledgerResults;
      result.addStep(
          'Verified account ledger entries', ledgerResults.isNotEmpty);

      // Step 7: Verify double-entry accounting rules
      log('Step 7: Verifying double-entry accounting rules');
      final doubleEntryValid = await _verifyDoubleEntryRules(journalEntries);
      result.addStep(
          'Verified double-entry rules (debits = credits)', doubleEntryValid);

      result.isSuccessful = result.steps.every((step) => step.isSuccessful);

      log('Integration test completed. Success: ${result.isSuccessful}');
      return result;
    } catch (e) {
      log('Integration test failed with error: $e');
      final result = IntegrationTestResult();
      result.addStep('Test failed with error: $e', false);
      return result;
    }
  }

  /// Create test Chart of Accounts
  Future<List<ChartOfAccountsModel>> _createTestAccounts() async {
    final accounts = <ChartOfAccountsModel>[];

    // Create test accounts for voucher integration
    final testAccountsData = [
      {
        'name': 'Test Company Freight Account',
        'number': '1001',
        'category': AccountCategory.assets,
        'type': AccountType.currentAssets,
      },
      {
        'name': 'Test Broker Fees Account',
        'number': '2001',
        'category': AccountCategory.liabilities,
        'type': AccountType.currentLiabilities,
      },
      {
        'name': 'Test Munshiana Account',
        'number': '3001',
        'category': AccountCategory.equity,
        'type': AccountType.retainedEarnings,
      },
      {
        'name': 'Test Sales Tax Account',
        'number': '2002',
        'category': AccountCategory.liabilities,
        'type': AccountType.currentLiabilities,
      },
      {
        'name': 'Test Freight Tax Account',
        'number': '2003',
        'category': AccountCategory.liabilities,
        'type': AccountType.currentLiabilities,
      },
      {
        'name': 'Test Profit Account',
        'number': '3002',
        'category': AccountCategory.equity,
        'type': AccountType.retainedEarnings,
      },
      {
        'name': 'Test Truck Freight Account',
        'number': '2004',
        'category': AccountCategory.liabilities,
        'type': AccountType.currentLiabilities,
      },
    ];

    for (final accountData in testAccountsData) {
      final account = ChartOfAccountsModel(
        id: 'test-${accountData['number']}',
        accountNumber: accountData['number'] as String,
        accountName: accountData['name'] as String,
        description: 'Test account for integration testing',
        category: accountData['category'] as AccountCategory,
        accountType: accountData['type'] as AccountType,
        parentAccountId: null,
        childAccountIds: [],
        isActive: true,
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: _uid,
      );

      try {
        await _accountsService.createAccount(account);
        accounts.add(account);
        log('Created test account: ${account.accountName}');
      } catch (e) {
        log('Failed to create test account ${account.accountName}: $e');
      }
    }

    return accounts;
  }

  /// Create test voucher with Chart of Accounts integration
  VoucherModel _createTestVoucher(List<ChartOfAccountsModel> accounts) {
    // Find accounts by type
    final companyFreightAccount = accounts.firstWhere(
      (acc) => acc.accountName.contains('Company Freight'),
    );
    final brokerAccount = accounts.firstWhere(
      (acc) => acc.accountName.contains('Broker Fees'),
    );
    final munshianaAccount = accounts.firstWhere(
      (acc) => acc.accountName.contains('Munshiana'),
    );
    final salesTaxAccount = accounts.firstWhere(
      (acc) => acc.accountName.contains('Sales Tax'),
    );
    final freightTaxAccount = accounts.firstWhere(
      (acc) => acc.accountName.contains('Freight Tax'),
    );
    final profitAccount = accounts.firstWhere(
      (acc) => acc.accountName.contains('Profit'),
    );
    final truckFreightAccount = accounts.firstWhere(
      (acc) => acc.accountName.contains('Truck Freight'),
    );

    return VoucherModel(
      voucherStatus: 'pending',
      departureDate: '01/01/2024',
      driverName: 'Test Driver',
      invoiceTasNumberList: [],
      invoiceBiltyNumberList: [],
      weightInTons: 10,
      voucherNumber: 'TEST-${DateTime.now().millisecondsSinceEpoch}',
      productName: 'Test Product',
      totalNumberOfBags: 100,
      brokerType: 'outsource',
      brokerName: 'Test Broker',
      brokerFees: 1000.0,
      munshianaFees: 500.0,
      munshianaAccount: munshianaAccount.accountName,
      brokerAccount: brokerAccount.accountName,
      driverPhoneNumber: '***********',
      truckNumber: 'TEST123',
      conveyNoteNumber: 'CN-TEST',
      totalFreight: 50000.0,
      calculatedProfit: 5000.0,
      calculatedTax: 2300.0,
      calculatedFreightTax: 7500.0,
      // Chart of Accounts integration fields
      companyFreightAccountId: companyFreightAccount.id,
      brokerAccountId: brokerAccount.id,
      munshianaAccountId: munshianaAccount.id,
      salesTaxAccountId: salesTaxAccount.id,
      freightTaxAccountId: freightTaxAccount.id,
      profitAccountId: profitAccount.id,
      truckFreightAccountId: truckFreightAccount.id,
      // Other required fields
      companyFreight: 50000.0,
      settledFreight: 0.0,
      paymentTransactions: [],
      belongsToDate: DateTime.now(),
      createdAt: DateTime.now(),
    );
  }

  /// Verify journal entries were created
  Future<List<Map<String, dynamic>>> _verifyJournalEntries(
      String voucherNumber) async {
    try {
      final result = await _ledgerService.getJournalEntries(
          _uid, voucherNumber, 'voucher');
      return result.fold(
        (failure) {
          log('Failed to get journal entries: ${failure.message}');
          return [];
        },
        (entries) {
          log('Found ${entries.length} journal entries for voucher: $voucherNumber');
          return entries
              .map((entry) => {
                    'id': entry.id,
                    'entryNumber': entry.entryNumber,
                    'description': entry.description,
                    'totalDebits': entry.totalDebits,
                    'totalCredits': entry.totalCredits,
                    'linesCount': entry.lines.length,
                  })
              .toList();
        },
      );
    } catch (e) {
      log('Error verifying journal entries: $e');
      return [];
    }
  }

  /// Verify account balances were updated
  Future<List<Map<String, dynamic>>> _verifyAccountBalances(
      List<ChartOfAccountsModel> accounts) async {
    final results = <Map<String, dynamic>>[];

    for (final account in accounts) {
      try {
        final balanceResult =
            await _ledgerService.getAccountBalance(account.id);
        final balance = balanceResult.fold(
          (failure) => 0.0,
          (balance) => balance,
        );

        results.add({
          'accountId': account.id,
          'accountName': account.accountName,
          'balance': balance,
          'hasTransactions': balance != 0.0,
        });

        log('Account ${account.accountName}: Balance = $balance');
      } catch (e) {
        log('Error getting balance for ${account.accountName}: $e');
      }
    }

    return results;
  }

  /// Verify account ledger entries were created
  Future<List<Map<String, dynamic>>> _verifyAccountLedgerEntries(
      List<ChartOfAccountsModel> accounts) async {
    final results = <Map<String, dynamic>>[];

    for (final account in accounts) {
      try {
        final ledgerEntries =
            await _accountLedgerService.getAccountLedgerEntries(
          accountId: account.id,
          limit: 10,
        );

        results.add({
          'accountId': account.id,
          'accountName': account.accountName,
          'ledgerEntriesCount': ledgerEntries.length,
          'hasLedgerEntries': ledgerEntries.isNotEmpty,
        });

        log('Account ${account.accountName}: ${ledgerEntries.length} ledger entries');
      } catch (e) {
        log('Error getting ledger entries for ${account.accountName}: $e');
      }
    }

    return results;
  }

  /// Verify double-entry accounting rules
  Future<bool> _verifyDoubleEntryRules(
      List<Map<String, dynamic>> journalEntries) async {
    for (final entry in journalEntries) {
      final totalDebits = entry['totalDebits'] as double;
      final totalCredits = entry['totalCredits'] as double;

      if ((totalDebits - totalCredits).abs() > 0.01) {
        // Allow for small rounding differences
        log('Double-entry rule violation in entry ${entry['entryNumber']}: Debits=$totalDebits, Credits=$totalCredits');
        return false;
      }
    }

    log('All journal entries follow double-entry accounting rules');
    return true;
  }

  /// Clean up test data
  Future<void> cleanupTestData(IntegrationTestResult result) async {
    try {
      log('Cleaning up test data');

      // Delete test accounts
      if (result.testAccounts != null) {
        for (final account in result.testAccounts!) {
          try {
            await _accountsService.deleteAccount(account.id);
            log('Deleted test account: ${account.accountName}');
          } catch (e) {
            log('Failed to delete test account ${account.accountName}: $e');
          }
        }
      }

      log('Test data cleanup completed');
    } catch (e) {
      log('Error during test data cleanup: $e');
    }
  }
}

/// Result class for integration test
class IntegrationTestResult {
  bool isSuccessful = false;
  final List<TestStep> steps = [];
  VoucherModel? testVoucher;
  List<ChartOfAccountsModel>? testAccounts;
  List<Map<String, dynamic>>? journalEntries;
  List<Map<String, dynamic>>? accountBalances;
  List<Map<String, dynamic>>? ledgerEntries;

  void addStep(String description, bool isSuccessful) {
    steps.add(TestStep(description, isSuccessful));
  }

  String get summary {
    final successCount = steps.where((step) => step.isSuccessful).length;
    return 'Integration Test: $successCount/${steps.length} steps passed';
  }
}

/// Individual test step
class TestStep {
  final String description;
  final bool isSuccessful;

  TestStep(this.description, this.isSuccessful);
}
