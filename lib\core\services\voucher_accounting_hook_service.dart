import 'dart:developer';
import '../../models/voucher_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';
import 'voucher_journal_integration_service.dart';

/// Service that hooks into voucher creation and updates to automatically generate journal entries
class VoucherAccountingHookService {
  static VoucherAccountingHookService? _instance;
  late final VoucherJournalIntegrationService _integrationService;
  late final AutomaticJournalEntryService _automaticJournalService;
  late final GeneralLedgerFirebaseService _generalLedgerService;
  late final JournalEntryFirebaseService _journalEntryService;
  late final TransactionAccountMappingService _mappingService;
  late final ChartOfAccountsFirebaseService _chartOfAccountsService;

  VoucherAccountingHookService._internal() {
    _initializeServices();
  }

  factory VoucherAccountingHookService() {
    _instance ??= VoucherAccountingHookService._internal();
    return _instance!;
  }

  void _initializeServices() {
    _chartOfAccountsService = ChartOfAccountsFirebaseService();
    _generalLedgerService = GeneralLedgerFirebaseService();
    _journalEntryService = JournalEntryFirebaseService();
    _mappingService = TransactionAccountMappingService(_chartOfAccountsService);
    _automaticJournalService = AutomaticJournalEntryService(
      _chartOfAccountsService,
    );
    _integrationService = VoucherJournalIntegrationService(
      _automaticJournalService,
      _generalLedgerService,
      _journalEntryService,
      _mappingService,
    );
  }

  /// Hook method to be called after voucher creation
  Future<void> onVoucherCreated(
      Map<String, dynamic> voucherData, String uid) async {
    try {
      log('Voucher accounting hook triggered for: ${voucherData['voucherNumber']}');

      // Convert to VoucherModel for processing
      VoucherModel voucher;
      try {
        voucher = VoucherModel.fromJson(voucherData);
      } catch (e) {
        log('Failed to convert voucher data to model: $e');
        return;
      }

      // Validate voucher for journal entry generation
      final validation = await _integrationService
          .validateVoucherForJournalEntry(voucher, uid);
      if (!validation.isValid) {
        log('Voucher validation failed: ${validation.issuesText}');
        return;
      }

      // Check if journal entries already exist
      final hasExisting = await _integrationService.hasExistingJournalEntries(
        voucher.voucherNumber,
        uid,
      );

      if (hasExisting) {
        log('Journal entries already exist for voucher: ${voucher.voucherNumber}');
        return;
      }

      // Process the voucher transaction
      final success =
          await _integrationService.processVoucherTransaction(voucher, uid);
      if (success) {
        log('Successfully created journal entries for voucher: ${voucher.voucherNumber}');
      } else {
        log('Failed to create journal entries for voucher: ${voucher.voucherNumber}');
      }
    } catch (e) {
      log('Error in voucher accounting hook: $e');
    }
  }

  /// Hook method to be called after voucher creation (VoucherModel version)
  Future<void> onVoucherCreatedFromModel(
      VoucherModel voucher, String uid) async {
    try {
      log('Voucher accounting hook triggered for model: ${voucher.voucherNumber}');

      // Validate voucher for journal entry generation
      final validation = await _integrationService
          .validateVoucherForJournalEntry(voucher, uid);
      if (!validation.isValid) {
        log('Voucher validation failed: ${validation.issuesText}');
        return;
      }

      // Check if journal entries already exist
      final hasExisting = await _integrationService.hasExistingJournalEntries(
        voucher.voucherNumber,
        uid,
      );

      if (hasExisting) {
        log('Journal entries already exist for voucher: ${voucher.voucherNumber}');
        return;
      }

      // Process the voucher transaction
      final success =
          await _integrationService.processVoucherTransaction(voucher, uid);
      if (success) {
        log('Successfully created journal entries for voucher: ${voucher.voucherNumber}');
      } else {
        log('Failed to create journal entries for voucher: ${voucher.voucherNumber}');
      }
    } catch (e) {
      log('Error in voucher accounting hook: $e');
    }
  }

  /// Hook method to be called before voucher deletion
  Future<void> onVoucherDeleted(
      Map<String, dynamic> voucherData, String uid) async {
    try {
      final voucherNumber = voucherData['voucherNumber'] as String? ?? '';
      log('Voucher deletion hook triggered for: $voucherNumber');

      if (voucherNumber.isEmpty) {
        log('Invalid voucher number for deletion hook');
        return;
      }

      // Reverse any existing journal entries
      final success = await _integrationService.reverseVoucherJournalEntries(
        voucherNumber,
        uid,
      );

      if (success) {
        log('Successfully reversed journal entries for deleted voucher: $voucherNumber');
      } else {
        log('Failed to reverse journal entries for deleted voucher: $voucherNumber');
      }
    } catch (e) {
      log('Error in voucher deletion hook: $e');
    }
  }

  /// Hook method to be called before voucher deletion (VoucherModel version)
  Future<void> onVoucherDeletedFromModel(
      VoucherModel voucher, String uid) async {
    try {
      log('Voucher deletion hook triggered for model: ${voucher.voucherNumber}');

      // Reverse any existing journal entries
      final success = await _integrationService.reverseVoucherJournalEntries(
        voucher.voucherNumber,
        uid,
      );

      if (success) {
        log('Successfully reversed journal entries for deleted voucher: ${voucher.voucherNumber}');
      } else {
        log('Failed to reverse journal entries for deleted voucher: ${voucher.voucherNumber}');
      }
    } catch (e) {
      log('Error in voucher deletion hook: $e');
    }
  }

  /// Hook method to be called after voucher update
  Future<void> onVoucherUpdated(
    Map<String, dynamic> oldVoucherData,
    Map<String, dynamic> newVoucherData,
    String uid,
  ) async {
    try {
      final voucherNumber = newVoucherData['voucherNumber'] as String? ?? '';
      log('Voucher update hook triggered for: $voucherNumber');

      // Convert to models for comparison
      VoucherModel oldVoucher, newVoucher;
      try {
        oldVoucher = VoucherModel.fromJson(oldVoucherData);
        newVoucher = VoucherModel.fromJson(newVoucherData);
      } catch (e) {
        log('Failed to convert voucher data to models: $e');
        return;
      }

      // Check if financial amounts changed
      if (_hasFinancialChanges(oldVoucher, newVoucher)) {
        log('Financial changes detected, updating journal entries');

        // Reverse old entries
        await _integrationService.reverseVoucherJournalEntries(
          oldVoucher.voucherNumber,
          uid,
        );

        // Create new entries
        await onVoucherCreatedFromModel(newVoucher, uid);
      }
    } catch (e) {
      log('Error in voucher update hook: $e');
    }
  }

  /// Check if voucher has financial changes that require journal entry updates
  bool _hasFinancialChanges(VoucherModel oldVoucher, VoucherModel newVoucher) {
    return oldVoucher.totalFreight != newVoucher.totalFreight ||
        oldVoucher.brokerFees != newVoucher.brokerFees ||
        oldVoucher.munshianaFees != newVoucher.munshianaFees ||
        oldVoucher.brokerAccount != newVoucher.brokerAccount ||
        oldVoucher.munshianaAccount != newVoucher.munshianaAccount;
  }

  /// Batch process existing vouchers to create journal entries
  Future<BatchProcessResult> processExistingVouchers(
      List<VoucherModel> vouchers, String uid) async {
    try {
      log('Processing ${vouchers.length} existing vouchers for journal entries');
      return await _integrationService.batchProcessVoucherTransactions(
          vouchers, uid);
    } catch (e) {
      log('Error processing existing vouchers: $e');
      return BatchProcessResult(
        totalProcessed: vouchers.length,
        successCount: 0,
        failureCount: vouchers.length,
        failedTransactionIds: vouchers.map((v) => v.voucherNumber).toList(),
      );
    }
  }

  /// Get journal entries for a specific voucher
  Future<List<dynamic>> getVoucherJournalEntries(
      String voucherNumber, String uid) async {
    return await _integrationService.getJournalEntriesForVoucher(
        voucherNumber, uid);
  }

  /// Get financial summary for a voucher
  Future<VoucherFinancialSummary> getVoucherFinancialSummary(
      VoucherModel voucher) async {
    return await _integrationService.getVoucherFinancialSummary(voucher);
  }

  /// Check if accounting integration is properly configured
  Future<bool> isAccountingConfigured(String uid) async {
    try {
      final mapping = await _mappingService.getVoucherAccountMapping(uid);
      return mapping != null;
    } catch (e) {
      log('Error checking accounting configuration: $e');
      return false;
    }
  }

  /// Get configuration status for UI display
  Future<AccountingConfigurationStatus> getConfigurationStatus(
      String uid) async {
    try {
      final mapping = await _mappingService.getVoucherAccountMapping(uid);

      if (mapping == null) {
        return AccountingConfigurationStatus(
          isConfigured: false,
          missingAccounts: [
            'Revenue Account',
            'Broker Expense Account',
            'Munshiana Expense Account'
          ],
          message:
              'Required accounts for voucher journal entries are not configured',
        );
      }

      return AccountingConfigurationStatus(
        isConfigured: true,
        missingAccounts: [],
        message: 'Accounting integration is properly configured',
      );
    } catch (e) {
      return AccountingConfigurationStatus(
        isConfigured: false,
        missingAccounts: [],
        message: 'Error checking configuration: $e',
      );
    }
  }
}

/// Configuration status for accounting integration
class AccountingConfigurationStatus {
  final bool isConfigured;
  final List<String> missingAccounts;
  final String message;

  AccountingConfigurationStatus({
    required this.isConfigured,
    required this.missingAccounts,
    required this.message,
  });
}
