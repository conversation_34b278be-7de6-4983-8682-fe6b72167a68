import 'dart:developer';
import 'package:get/get.dart';
import 'package:excel/excel.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';

import '../../../../../core/services/account_ledger_service.dart';
import '../../../../../models/finance/account_ledger_model.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';

/// Controller for managing account ledger entries display and interactions
class AccountLedgerController extends GetxController {
  final AccountLedgerService _ledgerService = AccountLedgerService();

  // Observable state
  final RxList<AccountLedgerModel> _ledgerEntries = <AccountLedgerModel>[].obs;
  final RxList<AccountLedgerModel> _filteredLedgerEntries = <AccountLedgerModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isError = false.obs;
  final RxString errorMessage = ''.obs;
  final RxDouble currentBalance = 0.0.obs;

  // Pagination
  final RxInt currentPage = 1.obs;
  final RxInt itemsPerPage = 25.obs;
  final RxInt totalLedgerEntries = 0.obs;

  // Filters
  final RxString searchQuery = ''.obs;
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Current account
  String? _currentAccountId;

  // Getters
  List<AccountLedgerModel> get ledgerEntries => _ledgerEntries;
  List<AccountLedgerModel> get filteredLedgerEntries => _filteredLedgerEntries;
  bool get hasLedgerEntries => _filteredLedgerEntries.isNotEmpty;

  int get totalPages => (totalLedgerEntries.value / itemsPerPage.value).ceil();
  bool get canGoPreviousPage => currentPage.value > 1;
  bool get canGoNextPage => currentPage.value < totalPages;
  int get currentPageStart => ((currentPage.value - 1) * itemsPerPage.value) + 1;
  int get currentPageEnd {
    final end = currentPage.value * itemsPerPage.value;
    return end > totalLedgerEntries.value ? totalLedgerEntries.value : end;
  }

  @override
  void onInit() {
    super.onInit();
    // Set up reactive filters
    ever(searchQuery, (_) => _applyFilters());
    ever(startDate, (_) => _applyFilters());
    ever(endDate, (_) => _applyFilters());
  }

  /// Load ledger entries for a specific account
  Future<void> loadLedgerEntriesForAccount(String accountId) async {
    try {
      _currentAccountId = accountId;
      isLoading.value = true;
      isError.value = false;
      errorMessage.value = '';

      log('Loading ledger entries for account: $accountId');

      // Load ledger entries
      final entries = await _ledgerService.getAccountLedgerEntries(
        accountId: accountId,
        limit: 1000, // Load more entries for better filtering
      );

      _ledgerEntries.value = entries;
      _applyFilters();

      // Get current balance
      final balance = await _ledgerService.getAccountBalanceFromLedger(accountId);
      currentBalance.value = balance;

      log('Loaded ${entries.length} ledger entries for account: $accountId');
    } catch (e) {
      log('Error loading ledger entries: $e');
      isError.value = true;
      errorMessage.value = 'Failed to load ledger entries: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// Refresh ledger entries
  Future<void> refreshLedgerEntries() async {
    if (_currentAccountId != null) {
      await loadLedgerEntriesForAccount(_currentAccountId!);
    }
  }

  /// Apply filters to ledger entries
  void _applyFilters() {
    var filtered = _ledgerEntries.toList();

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filtered = filtered.where((entry) {
        return entry.description.toLowerCase().contains(query) ||
            entry.referenceNumber.toLowerCase().contains(query) ||
            entry.referenceType.toLowerCase().contains(query);
      }).toList();
    }

    // Apply date filter
    if (startDate.value != null) {
      filtered = filtered.where((entry) {
        return entry.transactionDate.isAfter(startDate.value!) ||
            entry.transactionDate.isAtSameMomentAs(startDate.value!);
      }).toList();
    }

    if (endDate.value != null) {
      filtered = filtered.where((entry) {
        return entry.transactionDate.isBefore(endDate.value!.add(const Duration(days: 1)));
      }).toList();
    }

    _filteredLedgerEntries.value = filtered;
    totalLedgerEntries.value = filtered.length;
    currentPage.value = 1; // Reset to first page when filters change
  }

  /// Get current page ledger entries
  List<AccountLedgerModel> getCurrentPageLedgerEntries() {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;

    if (startIndex >= _filteredLedgerEntries.length) {
      return [];
    }

    return _filteredLedgerEntries.sublist(
      startIndex,
      endIndex > _filteredLedgerEntries.length ? _filteredLedgerEntries.length : endIndex,
    );
  }

  /// Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Set date filter
  void setDateFilter(DateTime start, DateTime end) {
    startDate.value = start;
    endDate.value = end;
  }

  /// Clear date filter
  void clearDateFilter() {
    startDate.value = null;
    endDate.value = null;
  }

  /// Navigate to next page
  void nextPage() {
    if (canGoNextPage) {
      currentPage.value++;
    }
  }

  /// Navigate to previous page
  void previousPage() {
    if (canGoPreviousPage) {
      currentPage.value--;
    }
  }

  /// Format date for display
  String formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Export ledger entries to Excel
  Future<void> exportLedgerToExcel(ChartOfAccountsModel account) async {
    try {
      isLoading.value = true;

      // Create Excel workbook
      final excel = Excel.createExcel();
      final sheet = excel['Account Ledger'];

      // Add headers
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Date');
      sheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('Reference');
      sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Description');
      sheet.cell(CellIndex.indexByString('D1')).value = TextCellValue('Debit Amount');
      sheet.cell(CellIndex.indexByString('E1')).value = TextCellValue('Credit Amount');
      sheet.cell(CellIndex.indexByString('F1')).value = TextCellValue('Running Balance');

      // Add account information
      sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Account: ${account.accountName}');
      sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Account Number: ${account.accountNumber}');
      sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('Account Type: ${account.accountType.displayName}');

      // Add data starting from row 6
      int row = 6;
      for (final entry in _filteredLedgerEntries) {
        sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(formatDate(entry.transactionDate));
        sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(entry.formattedReference);
        sheet.cell(CellIndex.indexByString('C$row')).value = TextCellValue(entry.description);
        sheet.cell(CellIndex.indexByString('D$row')).value = DoubleCellValue(entry.debitAmount);
        sheet.cell(CellIndex.indexByString('E$row')).value = DoubleCellValue(entry.creditAmount);
        sheet.cell(CellIndex.indexByString('F$row')).value = DoubleCellValue(entry.runningBalance);
        row++;
      }

      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'Account_Ledger_${account.accountName.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${directory.path}/$fileName';
      
      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      // Open file
      await OpenFile.open(filePath);

      Get.snackbar(
        'Export Successful',
        'Account ledger exported to $fileName',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      log('Error exporting ledger to Excel: $e');
      Get.snackbar(
        'Export Failed',
        'Failed to export account ledger: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
