import 'dart:developer' as dev;
import 'dart:math';
import 'package:uuid/uuid.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../repositories/chart_of_accounts_repository.dart';
import '../../../../../features/accounting/journal_entries/repositories/journal_entry_repository.dart';

/// Service for creating comprehensive mock data for Chart of Accounts testing
class MockDataService {
  final ChartOfAccountsRepository _repository;
  final JournalEntryRepository _journalRepository;
  final Random _random = Random();
  final Uuid _uuid = const Uuid();

  MockDataService(
    this._repository,
    this._journalRepository,
  );

  /// Create comprehensive mock data for logistics business
  Future<bool> createLogisticsMockData(String uid) async {
    try {
      dev.log('Creating comprehensive mock data for logistics business');

      // Create realistic chart of accounts for logistics business
      final accounts = await _createLogisticsChartOfAccounts(uid);

      // Create 6 months of actual journal entries and transactions
      await _createJournalEntriesAndTransactions(uid, accounts);

      dev.log(
          'Successfully created comprehensive mock data with journal entries');
      return true;
    } catch (e) {
      dev.log('Error creating mock data: $e');
      return false;
    }
  }

  /// Create realistic chart of accounts for logistics business
  Future<List<ChartOfAccountsModel>> _createLogisticsChartOfAccounts(
      String uid) async {
    final accounts = <ChartOfAccountsModel>[];
    final now = DateTime.now();

    // ASSETS (1000-1999)
    final assetAccounts = [
      // Current Assets (1000-1199)
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1000',
        accountName: 'Cash - Operating Account',
        description: 'Primary operating cash account',
        category: AccountCategory.assets,
        accountType: AccountType.cash,
        balance: 125000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1001',
        accountName: 'Petty Cash',
        description: 'Small cash fund for minor expenses',
        category: AccountCategory.assets,
        accountType: AccountType.cash,
        balance: 2500.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1010',
        accountName: 'Bank Account - HBL Main',
        description: 'Habib Bank Limited main operating account',
        category: AccountCategory.assets,
        accountType: AccountType.bank,
        balance: 450000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1011',
        accountName: 'Bank Account - UBL Payroll',
        description: 'United Bank Limited payroll account',
        category: AccountCategory.assets,
        accountType: AccountType.bank,
        balance: 85000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1100',
        accountName: 'Accounts Receivable - Trade',
        description: 'Money owed by customers for freight services',
        category: AccountCategory.assets,
        accountType: AccountType.accountsReceivable,
        balance: 320000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1101',
        accountName: 'Accounts Receivable - Brokers',
        description: 'Money owed by freight brokers',
        category: AccountCategory.assets,
        accountType: AccountType.accountsReceivable,
        balance: 75000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1200',
        accountName: 'Fuel Inventory',
        description: 'Diesel fuel inventory for fleet',
        category: AccountCategory.assets,
        accountType: AccountType.inventory,
        balance: 45000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1201',
        accountName: 'Spare Parts Inventory',
        description: 'Vehicle spare parts and maintenance supplies',
        category: AccountCategory.assets,
        accountType: AccountType.inventory,
        balance: 28000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1300',
        accountName: 'Prepaid Insurance',
        description: 'Vehicle and cargo insurance paid in advance',
        category: AccountCategory.assets,
        accountType: AccountType.currentAssets,
        balance: 15000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1301',
        accountName: 'Prepaid Licenses',
        description: 'Vehicle licenses and permits paid in advance',
        category: AccountCategory.assets,
        accountType: AccountType.currentAssets,
        balance: 8500.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),

      // Fixed Assets (1400-1999)
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1400',
        accountName: 'Trucks and Trailers',
        description: 'Fleet of trucks and trailers',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        balance: 2500000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1401',
        accountName: 'Accumulated Depreciation - Vehicles',
        description: 'Accumulated depreciation on fleet vehicles',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        balance: -450000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1500',
        accountName: 'Office Building',
        description: 'Office and warehouse building',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        balance: 800000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1501',
        accountName: 'Accumulated Depreciation - Building',
        description: 'Accumulated depreciation on building',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        balance: -120000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1600',
        accountName: 'Office Equipment',
        description: 'Computers, furniture, and office equipment',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        balance: 65000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '1601',
        accountName: 'Accumulated Depreciation - Equipment',
        description: 'Accumulated depreciation on office equipment',
        category: AccountCategory.assets,
        accountType: AccountType.fixedAssets,
        balance: -25000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
    ];

    accounts.addAll(assetAccounts);

    // LIABILITIES (2000-2999)
    final liabilityAccounts = [
      // Current Liabilities (2000-2299)
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '2000',
        accountName: 'Accounts Payable - Trade',
        description: 'Money owed to suppliers and vendors',
        category: AccountCategory.liabilities,
        accountType: AccountType.accountsPayable,
        balance: 85000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '2001',
        accountName: 'Accounts Payable - Fuel Suppliers',
        description: 'Money owed to fuel suppliers',
        category: AccountCategory.liabilities,
        accountType: AccountType.accountsPayable,
        balance: 35000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '2100',
        accountName: 'Accrued Salaries',
        description: 'Salaries earned but not yet paid',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        balance: 25000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '2101',
        accountName: 'Accrued Expenses',
        description: 'Expenses incurred but not yet paid',
        category: AccountCategory.liabilities,
        accountType: AccountType.currentLiabilities,
        balance: 15000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '2200',
        accountName: 'Short-term Loans',
        description: 'Bank loans payable within one year',
        category: AccountCategory.liabilities,
        accountType: AccountType.loansPayable,
        balance: 150000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '2201',
        accountName: 'Vehicle Loans',
        description: 'Loans for vehicle purchases',
        category: AccountCategory.liabilities,
        accountType: AccountType.loansPayable,
        balance: 350000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),

      // Long-term Liabilities (2300-2999)
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '2300',
        accountName: 'Long-term Bank Loan',
        description: 'Bank loan for business expansion',
        category: AccountCategory.liabilities,
        accountType: AccountType.longTermLiabilities,
        balance: 500000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '2301',
        accountName: 'Mortgage Payable',
        description: 'Mortgage on office building',
        category: AccountCategory.liabilities,
        accountType: AccountType.longTermLiabilities,
        balance: 400000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
    ];

    accounts.addAll(liabilityAccounts);

    // EQUITY (3000-3999)
    final equityAccounts = [
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '3000',
        accountName: 'Owner\'s Capital',
        description: 'Owner\'s initial investment in the business',
        category: AccountCategory.equity,
        accountType: AccountType.ownersEquity,
        balance: 1000000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '3100',
        accountName: 'Retained Earnings',
        description: 'Accumulated profits retained in the business',
        category: AccountCategory.equity,
        accountType: AccountType.retainedEarnings,
        balance: 250000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '3200',
        accountName: 'Owner\'s Drawings',
        description: 'Owner withdrawals from the business',
        category: AccountCategory.equity,
        accountType: AccountType.ownersEquity,
        balance: -50000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
    ];

    accounts.addAll(equityAccounts);

    // REVENUE (4000-4999)
    final revenueAccounts = [
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '4000',
        accountName: 'Freight Revenue',
        description: 'Revenue from freight transportation services',
        category: AccountCategory.revenue,
        accountType: AccountType.salesRevenue,
        balance: 850000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '4100',
        accountName: 'Logistics Services Revenue',
        description: 'Revenue from logistics and warehousing services',
        category: AccountCategory.revenue,
        accountType: AccountType.serviceRevenue,
        balance: 320000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '4200',
        accountName: 'Broker Commission Revenue',
        description: 'Commission earned from freight brokerage',
        category: AccountCategory.revenue,
        accountType: AccountType.serviceRevenue,
        balance: 125000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '4900',
        accountName: 'Other Income',
        description: 'Miscellaneous income and gains',
        category: AccountCategory.revenue,
        accountType: AccountType.otherRevenue,
        balance: 15000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
    ];

    accounts.addAll(revenueAccounts);

    // EXPENSES (5000-5999)
    final expenseAccounts = [
      // Operating Expenses (5000-5499)
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5000',
        accountName: 'Fuel Expenses',
        description: 'Diesel fuel costs for fleet vehicles',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 180000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5100',
        accountName: 'Vehicle Maintenance',
        description: 'Vehicle repair and maintenance costs',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 65000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5200',
        accountName: 'Driver Salaries',
        description: 'Salaries and wages for drivers',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 240000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5201',
        accountName: 'Office Staff Salaries',
        description: 'Salaries for office and administrative staff',
        category: AccountCategory.expenses,
        accountType: AccountType.administrativeExpenses,
        balance: 120000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5300',
        accountName: 'Insurance Expenses',
        description: 'Vehicle and cargo insurance costs',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 45000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5400',
        accountName: 'Office Rent',
        description: 'Monthly office and warehouse rent',
        category: AccountCategory.expenses,
        accountType: AccountType.administrativeExpenses,
        balance: 36000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5401',
        accountName: 'Utilities',
        description: 'Electricity, water, and gas expenses',
        category: AccountCategory.expenses,
        accountType: AccountType.administrativeExpenses,
        balance: 18000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5500',
        accountName: 'Interest Expense',
        description: 'Interest paid on loans and credit',
        category: AccountCategory.expenses,
        accountType: AccountType.interestExpense,
        balance: 25000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5600',
        accountName: 'Depreciation Expense',
        description: 'Depreciation of vehicles and equipment',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 85000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5700',
        accountName: 'Broker Fees',
        description: 'Fees paid to freight brokers',
        category: AccountCategory.expenses,
        accountType: AccountType.operatingExpenses,
        balance: 35000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
      ChartOfAccountsModel(
        id: _uuid.v4(),
        accountNumber: '5800',
        accountName: 'Professional Services',
        description: 'Legal, accounting, and consulting fees',
        category: AccountCategory.expenses,
        accountType: AccountType.administrativeExpenses,
        balance: 12000.0,
        isActive: true,
        createdAt: now.subtract(const Duration(days: 180)),
        uid: uid,
      ),
    ];

    accounts.addAll(expenseAccounts);

    // Create accounts in repository
    final allAccounts = [
      ...assetAccounts,
      ...liabilityAccounts,
      ...equityAccounts,
      ...revenueAccounts,
      ...expenseAccounts,
    ];
    for (final account in allAccounts) {
      final result = await _repository.createAccount(account);
      result.fold(
        (failure) => dev.log(
            'Failed to create account ${account.accountName}: ${failure.message}'),
        (success) => dev.log(
            'Created account: ${account.accountNumber} - ${account.accountName}'),
      );
    }

    return accounts;
  }

  /// Create actual journal entries and transactions for the past 6 months
  Future<void> _createJournalEntriesAndTransactions(
      String uid, List<ChartOfAccountsModel> accounts) async {
    dev.log('Creating 6 months of actual journal entries and transactions...');

    final now = DateTime.now();
    final startDate = now.subtract(const Duration(days: 180));

    // Generate journal entries for each month
    for (int month = 0; month < 6; month++) {
      final monthStart = DateTime(startDate.year, startDate.month + month, 1);
      final monthEnd = DateTime(startDate.year, startDate.month + month + 1, 0);

      await _createMonthlyJournalEntries(uid, accounts, monthStart, monthEnd);
    }
  }

  /// Create journal entries for a specific month
  Future<void> _createMonthlyJournalEntries(
    String uid,
    List<ChartOfAccountsModel> accounts,
    DateTime monthStart,
    DateTime monthEnd,
  ) async {
    // Generate 15-25 journal entries per month
    final entryCount = 15 + _random.nextInt(11);

    for (int i = 0; i < entryCount; i++) {
      final entryDate = _randomDateInRange(monthStart, monthEnd);
      await _createRandomJournalEntry(uid, accounts, entryDate);
    }
  }

  /// Create a random realistic journal entry
  Future<void> _createRandomJournalEntry(
    String uid,
    List<ChartOfAccountsModel> accounts,
    DateTime entryDate,
  ) async {
    // Journal entry types with weights
    final entryTypes = [
      'freight_revenue',
      'fuel_expense',
      'maintenance_expense',
      'salary_payment',
      'insurance_payment',
      'customer_payment',
      'supplier_payment',
      'loan_payment',
    ];

    final entryType = entryTypes[_random.nextInt(entryTypes.length)];

    switch (entryType) {
      case 'freight_revenue':
        await _createFreightRevenueJournalEntry(uid, accounts, entryDate);
        break;
      case 'fuel_expense':
        await _createFuelExpenseJournalEntry(uid, accounts, entryDate);
        break;
      case 'maintenance_expense':
        await _createMaintenanceExpenseJournalEntry(uid, accounts, entryDate);
        break;
      case 'salary_payment':
        await _createSalaryPaymentJournalEntry(uid, accounts, entryDate);
        break;
      case 'customer_payment':
        await _createCustomerPaymentJournalEntry(uid, accounts, entryDate);
        break;
      default:
        // Create a simple journal entry
        break;
    }
  }

  /// Generate random date within range
  DateTime _randomDateInRange(DateTime start, DateTime end) {
    final difference = end.difference(start).inDays;
    final randomDays = _random.nextInt(difference + 1);
    return start.add(Duration(days: randomDays));
  }

  /// Helper method to create and post journal entry
  Future<void> _createJournalEntry({
    required String uid,
    required DateTime entryDate,
    required String description,
    required List<JournalEntryLineModel> lines,
  }) async {
    try {
      // Calculate totals
      final totalDebits =
          lines.fold(0.0, (sum, line) => sum + line.debitAmount);
      final totalCredits =
          lines.fold(0.0, (sum, line) => sum + line.creditAmount);

      // Validate double-entry bookkeeping
      if ((totalDebits - totalCredits).abs() > 0.01) {
        dev.log(
            'Warning: Journal entry not balanced - Debits: $totalDebits, Credits: $totalCredits');
        return;
      }

      // Create journal entry
      final journalEntry = JournalEntryModel(
        id: _uuid.v4(),
        entryNumber: '', // Will be auto-generated by service
        entryDate: entryDate,
        description: description,
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines
            .map((line) => line.copyWith(journalEntryId: _uuid.v4()))
            .toList(),
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        sourceTransactionType: 'mock_data',
        createdAt: DateTime.now(),
        createdBy: 'mock_data_service',
        uid: uid,
      );

      // Post journal entry through repository
      final result = await _journalRepository.createJournalEntry(journalEntry);
      result.fold(
        (failure) =>
            dev.log('Failed to create journal entry: ${failure.message}'),
        (success) => dev.log(
            'Successfully created journal entry: ${journalEntry.description}'),
      );
    } catch (e) {
      dev.log('Error creating journal entry: $e');
    }
  }

  /// Create freight revenue journal entry
  Future<void> _createFreightRevenueJournalEntry(
    String uid,
    List<ChartOfAccountsModel> accounts,
    DateTime date,
  ) async {
    final amount = 15000 + _random.nextDouble() * 35000; // 15k to 50k

    // Find accounts
    final receivableAccount = accounts.firstWhere(
      (a) => a.accountNumber == '1200',
      orElse: () => accounts.first,
    );
    final revenueAccount = accounts.firstWhere(
      (a) => a.accountNumber == '4000',
      orElse: () => accounts.first,
    );

    // Create journal entry
    await _createJournalEntry(
      uid: uid,
      entryDate: date,
      description: 'Freight revenue for delivery services',
      lines: [
        // Dr. Accounts Receivable
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: receivableAccount.id,
          accountNumber: receivableAccount.accountNumber,
          accountName: receivableAccount.accountName,
          debitAmount: amount,
          creditAmount: 0.0,
          description: 'Freight revenue - customer invoice',
          referenceType: 'freight_revenue',
          createdAt: date,
        ),
        // Cr. Freight Revenue
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: revenueAccount.id,
          accountNumber: revenueAccount.accountNumber,
          accountName: revenueAccount.accountName,
          debitAmount: 0.0,
          creditAmount: amount,
          description: 'Freight revenue earned',
          referenceType: 'freight_revenue',
          createdAt: date,
        ),
      ],
    );

    dev.log(
        'Created freight revenue journal entry: ${amount.toStringAsFixed(2)} on ${date.toIso8601String()}');
  }

  /// Create fuel expense journal entry
  Future<void> _createFuelExpenseJournalEntry(
    String uid,
    List<ChartOfAccountsModel> accounts,
    DateTime date,
  ) async {
    final amount = 8000 + _random.nextDouble() * 12000; // 8k to 20k

    // Find accounts
    final fuelExpenseAccount = accounts.firstWhere(
      (a) => a.accountNumber == '5001',
      orElse: () => accounts.first,
    );
    final cashAccount = accounts.firstWhere(
      (a) => a.accountNumber == '1000',
      orElse: () => accounts.first,
    );

    // Create journal entry
    await _createJournalEntry(
      uid: uid,
      entryDate: date,
      description: 'Fuel expense for vehicle operations',
      lines: [
        // Dr. Fuel Expenses
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: fuelExpenseAccount.id,
          accountNumber: fuelExpenseAccount.accountNumber,
          accountName: fuelExpenseAccount.accountName,
          debitAmount: amount,
          creditAmount: 0.0,
          description: 'Fuel purchase for fleet',
          referenceType: 'fuel_expense',
          createdAt: date,
        ),
        // Cr. Cash Account
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: cashAccount.id,
          accountNumber: cashAccount.accountNumber,
          accountName: cashAccount.accountName,
          debitAmount: 0.0,
          creditAmount: amount,
          description: 'Cash payment for fuel',
          referenceType: 'fuel_expense',
          createdAt: date,
        ),
      ],
    );

    dev.log(
        'Created fuel expense journal entry: ${amount.toStringAsFixed(2)} on ${date.toIso8601String()}');
  }

  /// Create maintenance expense journal entry
  Future<void> _createMaintenanceExpenseJournalEntry(
    String uid,
    List<ChartOfAccountsModel> accounts,
    DateTime date,
  ) async {
    final amount = 2000 + _random.nextDouble() * 8000; // 2k to 10k

    // Find accounts
    final maintenanceAccount = accounts.firstWhere(
      (a) => a.accountNumber == '5002',
      orElse: () => accounts.first,
    );
    final payableAccount = accounts.firstWhere(
      (a) => a.accountNumber == '2100',
      orElse: () => accounts.first,
    );

    // Create journal entry
    await _createJournalEntry(
      uid: uid,
      entryDate: date,
      description: 'Vehicle maintenance and repairs',
      lines: [
        // Dr. Vehicle Maintenance
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: maintenanceAccount.id,
          accountNumber: maintenanceAccount.accountNumber,
          accountName: maintenanceAccount.accountName,
          debitAmount: amount,
          creditAmount: 0.0,
          description: 'Vehicle maintenance expense',
          referenceType: 'maintenance_expense',
          createdAt: date,
        ),
        // Cr. Accounts Payable
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: payableAccount.id,
          accountNumber: payableAccount.accountNumber,
          accountName: payableAccount.accountName,
          debitAmount: 0.0,
          creditAmount: amount,
          description: 'Amount owed to maintenance provider',
          referenceType: 'maintenance_expense',
          createdAt: date,
        ),
      ],
    );

    dev.log(
        'Created maintenance expense journal entry: ${amount.toStringAsFixed(2)} on ${date.toIso8601String()}');
  }

  /// Create salary payment journal entry
  Future<void> _createSalaryPaymentJournalEntry(
    String uid,
    List<ChartOfAccountsModel> accounts,
    DateTime date,
  ) async {
    final amount = 45000 + _random.nextDouble() * 25000; // 45k to 70k

    // Find accounts
    final salaryAccount = accounts.firstWhere(
      (a) => a.accountNumber == '5003',
      orElse: () => accounts.first,
    );
    final bankAccount = accounts.firstWhere(
      (a) => a.accountNumber == '1001',
      orElse: () => accounts.first,
    );

    // Create journal entry
    await _createJournalEntry(
      uid: uid,
      entryDate: date,
      description: 'Monthly salary payments to employees',
      lines: [
        // Dr. Salaries and Wages
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: salaryAccount.id,
          accountNumber: salaryAccount.accountNumber,
          accountName: salaryAccount.accountName,
          debitAmount: amount,
          creditAmount: 0.0,
          description: 'Employee salary expense',
          referenceType: 'salary_payment',
          createdAt: date,
        ),
        // Cr. Bank Account - Payroll
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: bankAccount.id,
          accountNumber: bankAccount.accountNumber,
          accountName: bankAccount.accountName,
          debitAmount: 0.0,
          creditAmount: amount,
          description: 'Bank transfer for salary payments',
          referenceType: 'salary_payment',
          createdAt: date,
        ),
      ],
    );

    dev.log(
        'Created salary payment journal entry: ${amount.toStringAsFixed(2)} on ${date.toIso8601String()}');
  }

  /// Create customer payment journal entry
  Future<void> _createCustomerPaymentJournalEntry(
    String uid,
    List<ChartOfAccountsModel> accounts,
    DateTime date,
  ) async {
    final amount = 20000 + _random.nextDouble() * 40000; // 20k to 60k

    // Find accounts
    final bankAccount = accounts.firstWhere(
      (a) => a.accountNumber == '1001',
      orElse: () => accounts.first,
    );
    final receivableAccount = accounts.firstWhere(
      (a) => a.accountNumber == '1200',
      orElse: () => accounts.first,
    );

    // Create journal entry
    await _createJournalEntry(
      uid: uid,
      entryDate: date,
      description: 'Customer payment received',
      lines: [
        // Dr. Bank Account
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: bankAccount.id,
          accountNumber: bankAccount.accountNumber,
          accountName: bankAccount.accountName,
          debitAmount: amount,
          creditAmount: 0.0,
          description: 'Customer payment received',
          referenceType: 'customer_payment',
          createdAt: date,
        ),
        // Cr. Accounts Receivable
        JournalEntryLineModel(
          id: _uuid.v4(),
          journalEntryId: '',
          accountId: receivableAccount.id,
          accountNumber: receivableAccount.accountNumber,
          accountName: receivableAccount.accountName,
          debitAmount: 0.0,
          creditAmount: amount,
          description: 'Customer payment applied to receivable',
          referenceType: 'customer_payment',
          createdAt: date,
        ),
      ],
    );

    dev.log(
        'Created customer payment journal entry: ${amount.toStringAsFixed(2)} on ${date.toIso8601String()}');
  }

  /// Clear all mock data
  Future<bool> clearMockData(String uid) async {
    try {
      dev.log('Clearing all mock data for company: $uid');

      // This would clear all accounts and transactions for the company
      // Implementation would depend on repository methods

      dev.log('Successfully cleared mock data');
      return true;
    } catch (e) {
      dev.log('Error clearing mock data: $e');
      return false;
    }
  }

  /// Get mock data statistics
  Future<Map<String, dynamic>> getMockDataStats(String uid) async {
    try {
      final accountsResult = await _repository.getAccounts();

      // Get journal entries count
      final journalEntriesResult = await _journalRepository.getJournalEntries();

      return accountsResult.fold(
        (failure) => {'error': failure.message},
        (accounts) => journalEntriesResult.fold(
          (failure) => {
            'totalAccounts': accounts.length,
            'assetAccounts': accounts
                .where((a) => a.category == AccountCategory.assets)
                .length,
            'liabilityAccounts': accounts
                .where((a) => a.category == AccountCategory.liabilities)
                .length,
            'equityAccounts': accounts
                .where((a) => a.category == AccountCategory.equity)
                .length,
            'revenueAccounts': accounts
                .where((a) => a.category == AccountCategory.revenue)
                .length,
            'expenseAccounts': accounts
                .where((a) => a.category == AccountCategory.expenses)
                .length,
            'totalBalance':
                accounts.fold(0.0, (sum, account) => sum + account.balance),
            'totalTransactions':
                0, // Default if journal entries can't be loaded
            'totalTransactionLines': 0,
          },
          (journalEntries) {
            final totalLines = journalEntries.fold(
                0, (sum, entry) => sum + entry.lines.length);

            return {
              'totalAccounts': accounts.length,
              'assetAccounts': accounts
                  .where((a) => a.category == AccountCategory.assets)
                  .length,
              'liabilityAccounts': accounts
                  .where((a) => a.category == AccountCategory.liabilities)
                  .length,
              'equityAccounts': accounts
                  .where((a) => a.category == AccountCategory.equity)
                  .length,
              'revenueAccounts': accounts
                  .where((a) => a.category == AccountCategory.revenue)
                  .length,
              'expenseAccounts': accounts
                  .where((a) => a.category == AccountCategory.expenses)
                  .length,
              'totalBalance':
                  accounts.fold(0.0, (sum, account) => sum + account.balance),
              'totalTransactions': journalEntries.length,
              'totalTransactionLines': totalLines,
            };
          },
        ),
      );
    } catch (e) {
      return {'error': e.toString()};
    }
  }
}
