import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../../features/home/<USER>/theme.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../controllers/chart_of_accounts_controller.dart';
import '../widgets/chart_of_accounts_form.dart';
import 'account_journal_transactions_view.dart';

class ChartOfAccountsView extends StatelessWidget {
  const ChartOfAccountsView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ChartOfAccountsController>();

    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    // Use Stack to overlay the form drawer
    return Stack(
      children: [
        // Main content
        Container(
          color: notifier.getBgColor,
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and buttons
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: _buildResponsiveHeader(context, controller),
                ),
                const SizedBox(height: 20),
                // Filter section
                _buildFilterSection(controller, context, notifier),
                const SizedBox(height: 20),
                // Simple body content
                _buildSimpleBody(controller, context),
              ],
            ),
          ),
        ),
        // Form drawer overlay
        Obx(() {
          if (!controller.isDrawerOpen.value) {
            return const SizedBox.shrink();
          }
          return Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: Get.width < 650 ? Get.width : 400,
            child: const ChartOfAccountsForm(),
          );
        }),
      ],
    );
  }

  Widget _buildSimpleBody(
      ChartOfAccountsController controller, BuildContext context) {
    return Obx(() {
      // Show loading indicator for initial load
      if (controller.isLoading.value && controller.paginatedAccounts.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(50),
            child: CircularProgressIndicator(),
          ),
        );
      }

      // Show empty state if no accounts exist
      if (controller.paginatedAccounts.isEmpty && !controller.isLoading.value) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.account_balance,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No accounts found',
                  style: Theme.of(Get.context!).textTheme.titleLarge?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Create your first account to get started',
                  style: Theme.of(Get.context!).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[500],
                      ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => controller.openDrawerForNewAccount(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Account'),
                ),
              ],
            ),
          ),
        );
      }

      // Show accounts list
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Simple accounts list
          ...controller.paginatedAccounts.map((account) => Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getCategoryColor(account.category),
                    child: Text(
                      account.accountNumber.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(account.accountName),
                  subtitle: Text(
                      '${account.accountType.displayName} • ${account.category.displayName}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.receipt_long),
                        tooltip: 'View Transactions',
                        onPressed: () {
                          _navigateToTransactions(account);
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.bug_report),
                        tooltip: 'Test Transactions',
                        onPressed: () {
                          controller.testAccountTransactions(
                              account.id, account.accountName);
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () {
                          controller.startEditing(account);
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          _showDeleteConfirmation(
                              Get.context!, account, controller);
                        },
                      ),
                    ],
                  ),
                  onTap: () {
                    controller.startEditing(account);
                  },
                ),
              )),
          // Simple pagination info
          if (controller.paginatedAccounts.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Showing ${controller.paginatedAccounts.length} accounts',
                style: Theme.of(Get.context!).textTheme.bodyMedium,
              ),
            ),
        ],
      );
    });
  }

  Widget _buildFilterSection(ChartOfAccountsController controller,
      BuildContext context, ColorNotifier notifier) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filters',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            // Search field
            TextField(
              controller: controller.searchController,
              decoration: InputDecoration(
                hintText: 'Search by account name, number, or description...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
            const SizedBox(height: 16),
            // Filter controls
            Wrap(
              spacing: 16,
              runSpacing: 12,
              children: [
                // Category filter
                _buildCategoryFilter(controller),
                // Account type filter
                _buildAccountTypeFilter(controller),
                // Status filter
                _buildStatusFilter(controller),
              ],
            ),
            const SizedBox(height: 12),
            // Quick filter buttons and clear filters
            Row(
              children: [
                Expanded(
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildQuickFilterChip(
                          'Assets', AccountCategory.assets, controller),
                      _buildQuickFilterChip('Liabilities',
                          AccountCategory.liabilities, controller),
                      _buildQuickFilterChip(
                          'Equity', AccountCategory.equity, controller),
                      _buildQuickFilterChip(
                          'Revenue', AccountCategory.revenue, controller),
                      _buildQuickFilterChip(
                          'Expenses', AccountCategory.expenses, controller),
                    ],
                  ),
                ),
                TextButton.icon(
                  onPressed: controller.clearFilters,
                  icon: const Icon(Icons.clear_all),
                  label: const Text('Clear Filters'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryFilter(ChartOfAccountsController controller) {
    return SizedBox(
      width: 200,
      child: Obx(() => DropdownButtonFormField<AccountCategory>(
            value: controller.selectedCategoryFilter.value,
            decoration: InputDecoration(
              labelText: 'Category',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: [
              const DropdownMenuItem<AccountCategory>(
                value: null,
                child: Text('All Categories'),
              ),
              ...AccountCategory.values.map((category) {
                return DropdownMenuItem<AccountCategory>(
                  value: category,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: _getCategoryColor(category),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(category.displayName),
                    ],
                  ),
                );
              }),
            ],
            onChanged: (value) {
              controller.setCategoryFilter(value);
            },
          )),
    );
  }

  Widget _buildAccountTypeFilter(ChartOfAccountsController controller) {
    return SizedBox(
      width: 200,
      child: Obx(() {
        final selectedCategory = controller.selectedCategoryFilter.value;
        final availableTypes = selectedCategory != null
            ? AccountType.getTypesForCategory(selectedCategory)
            : AccountType.values;

        return DropdownButtonFormField<AccountType>(
          value: controller.selectedAccountTypeFilter.value,
          decoration: InputDecoration(
            labelText: 'Account Type',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: [
            const DropdownMenuItem<AccountType>(
              value: null,
              child: Text('All Types'),
            ),
            ...availableTypes.map((type) {
              return DropdownMenuItem<AccountType>(
                value: type,
                child: Text(type.displayName),
              );
            }),
          ],
          onChanged: (value) {
            controller.setAccountTypeFilter(value);
          },
        );
      }),
    );
  }

  Widget _buildStatusFilter(ChartOfAccountsController controller) {
    return SizedBox(
      width: 150,
      child: Obx(() => DropdownButtonFormField<bool>(
            value: controller.showInactiveAccounts.value ? false : null,
            decoration: InputDecoration(
              labelText: 'Status',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: const [
              DropdownMenuItem<bool>(
                value: null,
                child: Text('All'),
              ),
              DropdownMenuItem<bool>(
                value: true,
                child: Text('Active'),
              ),
              DropdownMenuItem<bool>(
                value: false,
                child: Text('Inactive'),
              ),
            ],
            onChanged: (value) {
              if (value == null) {
                controller.showInactiveAccounts.value = true; // Show all
              } else if (value == true) {
                controller.showInactiveAccounts.value = false; // Active only
              } else {
                controller.showInactiveAccounts.value = true; // Show inactive
              }
            },
          )),
    );
  }

  Widget _buildQuickFilterChip(String label, AccountCategory category,
      ChartOfAccountsController controller) {
    return Obx(() => FilterChip(
          label: Text(label),
          selected: controller.selectedCategoryFilter.value == category,
          onSelected: (selected) {
            if (selected) {
              controller.setCategoryFilter(category);
            } else {
              controller.setCategoryFilter(null);
            }
          },
          selectedColor: _getCategoryColor(category).withValues(alpha: 0.2),
          checkmarkColor: _getCategoryColor(category),
        ));
  }

  Color _getCategoryColor(AccountCategory category) {
    switch (category) {
      case AccountCategory.assets:
        return Colors.green;
      case AccountCategory.liabilities:
        return Colors.red;
      case AccountCategory.equity:
        return Colors.blue;
      case AccountCategory.revenue:
        return Colors.orange;
      case AccountCategory.expenses:
        return Colors.purple;
    }
  }

  void _showDeleteConfirmation(BuildContext context,
      ChartOfAccountsModel account, ChartOfAccountsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text(
          'Are you sure you want to delete "${account.accountName}"?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.deleteAccount(account);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Build responsive header with title and action buttons
  Widget _buildResponsiveHeader(
      BuildContext context, ChartOfAccountsController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // For very small screens, stack the title and buttons vertically
        if (constraints.maxWidth < 600) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Chart of Accounts',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  _buildMockDataButton(controller),
                  const SizedBox(width: 8),
                  _buildAddAccountButton(controller),
                ],
              ),
            ],
          );
        }

        // For larger screens, use horizontal layout
        return Flex(
          direction: Axis.horizontal,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              flex: 2,
              child: Text(
                'Chart of Accounts',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Flexible(
              flex: 1,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  _buildMockDataButton(controller),
                  const SizedBox(width: 8),
                  _buildAddAccountButton(controller),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build Mock Data PopupMenuButton
  Widget _buildMockDataButton(ChartOfAccountsController controller) {
    return PopupMenuButton<String>(
      icon: const Icon(
        Icons.data_usage,
        size: 24,
        color: Colors.blue,
      ),
      tooltip: 'Mock Data Options',
      onSelected: (value) {
        switch (value) {
          case 'create':
            controller.createMockData();
            break;
          case 'stats':
            controller.getMockDataStats();
            break;
          case 'clear':
            _showClearMockDataDialog(Get.context!, controller);
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'create',
          child: Row(
            children: [
              Icon(Icons.add_circle, color: Colors.green),
              SizedBox(width: 8),
              Text('Create Mock Data'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'stats',
          child: Row(
            children: [
              Icon(Icons.info, color: Colors.blue),
              SizedBox(width: 8),
              Text('View Statistics'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'clear',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 8),
              Text('Clear Mock Data'),
            ],
          ),
        ),
      ],
    );
  }

  /// Build Add Account Button
  Widget _buildAddAccountButton(ChartOfAccountsController controller) {
    return ElevatedButton.icon(
      onPressed: controller.openDrawerForNewAccount,
      icon: const Icon(Icons.add),
      label: const Text('Add Account'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
    );
  }

  /// Show confirmation dialog for clearing mock data
  void _showClearMockDataDialog(
      BuildContext context, ChartOfAccountsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Mock Data'),
        content: const Text(
          'Are you sure you want to clear all mock data? This action cannot be undone.\n\n'
          'This will remove all test accounts and transaction history.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.clearMockData();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear Data'),
          ),
        ],
      ),
    );
  }

  /// Navigate to account journal transactions view
  void _navigateToTransactions(ChartOfAccountsModel account) {
    // Get current user's UID
    final uid = FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

    Get.to(() => AccountJournalTransactionsView(
          account: account,
          uid: uid,
        ));
  }
}
