import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/widgets/payment_transaction_dialog.dart';
import 'package:logestics/core/utils/widgets/string_tag_controller.dart';
import 'package:logestics/features/company/presentation/conrollers/company_controller.dart';
import 'package:logestics/features/finance/accounts/presentation/controllers/account_controller.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/features/finance/accounts/repositories/account_transaction_repository.dart';
import 'package:logestics/features/accounting/chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';
import 'package:logestics/features/accounting/chart_of_accounts/services/mock_data_service.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/core/services/voucher_chart_of_accounts_service.dart';

import 'package:logestics/features/finance/brokers/repositories/broker_repository.dart';
import 'package:logestics/features/finance/check_usage/repositories/check_usage_repository.dart';
import 'package:logestics/features/finance/deposits/presentation/controllers/deposit_controller.dart';
import 'package:logestics/features/finance/deposits/repository/deposit_repository.dart';
import 'package:logestics/features/finance/expenses/repositories/expense_repository.dart';
import 'package:logestics/features/finance/fuel_cards/presentation/controllers/fuel_card_controller.dart';
import 'package:logestics/features/finance/payees/repositories/payee_repository.dart';
import 'package:logestics/features/voucher/presentation/controllers/voucher_list_controller.dart';
import 'package:logestics/features/voucher/use_cases/create_voucher_use_case.dart';
import 'package:logestics/features/voucher/use_cases/update_voucher_use_case.dart';
import 'package:logestics/firebase_service/finance/account_firebase_service.dart';
import 'package:logestics/firebase_service/finance/account_transaction_firebase_service.dart';
import 'package:logestics/firebase_service/finance/deposit_firebase_service.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';
import 'package:logestics/models/finance/loan_model.dart';
import 'package:logestics/firebase_service/finance/expense_firebase_service.dart';
import 'package:logestics/firebase_service/finance/fuel_card_usage_firebase_service.dart';
import 'package:logestics/firebase_service/finance/payee_firebase_service.dart';
import 'package:logestics/main.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/broker_model.dart';
import 'package:logestics/models/finance/check_usage_model.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/models/finance/expense_model.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:logestics/models/finance/fuel_card_usage_model.dart';
import 'package:logestics/models/finance/payee_model.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/voucher_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/enums/broker_enums.dart';

class AddVoucherController extends GetxController {
  var editMode = false;
  final VoucherModel? currentVoucher;
  var companyController = Get.find<CompanyController>();
  final CreateVoucherUseCase createVoucherUseCase;
  final UpdateVoucherUseCase updateVoucherUseCase;

  // For payee and account management
  final payeeRepository = PayeeRepositoryImpl(PayeeFirebaseService());
  final brokerRepository = Get.find<BrokerRepository>();
  final checkUsageRepository = Get.find<CheckUsageRepository>();
  final accountRepository = AccountRepositoryImpl(AccountFirebaseService());
  final accountTransactionRepository =
      AccountTransactionRepositoryImpl(AccountTransactionFirebaseService());

  final RxList<PayeeModel> payees = <PayeeModel>[].obs;
  final RxList<AccountModel> accounts = <AccountModel>[].obs;

  // Form keys for payee and account forms
  final payeeFormKey = GlobalKey<FormState>();
  final accountFormKey = GlobalKey<FormState>();

  // Controllers for payee form
  final payeeNameController = TextEditingController();
  final payeePhoneController = TextEditingController();

  // Controllers for account form
  final accountNameController = TextEditingController();
  final accountNumberController = TextEditingController();
  final initialBalanceController = TextEditingController();
  final branchCodeController = TextEditingController();
  final branchAddressController = TextEditingController();

  // Selected payee and accounts (legacy)
  final selectedPayee = Rxn<PayeeModel>();
  final selectedBrokerAccount = Rxn<AccountModel>();
  final selectedMunshianaAccount = Rxn<AccountModel>();
  final selectedCompanyFreightAccount = Rxn<AccountModel>();
  final selectedSalesTaxAccount = Rxn<AccountModel>();
  final selectedFreightTaxAccount = Rxn<AccountModel>();
  final selectedProfitAccount = Rxn<AccountModel>();

  // Chart of Accounts selections (new)
  final selectedBrokerChartAccount = Rxn<ChartOfAccountsModel>();
  final selectedMunshianaChartAccount = Rxn<ChartOfAccountsModel>();
  final selectedCompanyFreightChartAccount = Rxn<ChartOfAccountsModel>();
  final selectedSalesTaxChartAccount = Rxn<ChartOfAccountsModel>();
  final selectedFreightTaxChartAccount = Rxn<ChartOfAccountsModel>();
  final selectedProfitChartAccount = Rxn<ChartOfAccountsModel>();
  final selectedTruckFreightChartAccount = Rxn<ChartOfAccountsModel>();

  // Tax Authority Selection (15% tax options)
  final selectedTaxAuthorities = <String>[].obs;
  final taxAuthorityError = RxnString();

  // Company selection for Own broker type
  final availableCompanies = <UserModel>[].obs;
  final selectedBrokerCompany = Rxn<UserModel>();

  // CRITICAL FIX: Local payment transactions that are NOT saved to database during creation
  final localPaymentTransactions = <PaymentTransactionModel>[].obs;
  final existingPaymentTransactions =
      <PaymentTransactionModel>[].obs; // Only for edit mode
  final fuelCards = <FuelCardModel>[].obs;
  final settledFreight = RxDouble(0.0);

  // Initialize the tag controller lazily to avoid multiple registrations
  late StringTagController stringTagController;

  // Flag to track if tags have been initialized
  var tagsInitialized = false.obs;

  // For fuel card management
  late FuelCardController fuelCardController;

  AddVoucherController(
      {this.currentVoucher,
      required this.createVoucherUseCase,
      required this.updateVoucherUseCase}) {
    // Initialize the stringTagController in the constructor
    stringTagController = StringTagController();
  }

  // Tax Authority Constants
  static const List<String> availableTaxAuthorities = [
    'SRB (Sindh Revenue Board)',
    'PRA (Punjab Revenue Authority)',
    'BRA (Balochistan Revenue Authority)',
    'KRA (Khyber Revenue Authority)',
  ];

  var addVoucherFormStateKey = GlobalKey<FormState>();

  // TextEditingControllers for each field
  late TextEditingController voucherNumberController;
  late TextEditingController departureDateController;
  late TextEditingController productNameController;
  late TextEditingController totalNumberOfBagsController;

  late TextEditingController brokerNameController;
  late TextEditingController driverNameController;
  late TextEditingController driverPhoneNumberController;

  late TextEditingController truckNumberController;
  late TextEditingController conveyNoteNumberController;

  late TextEditingController totalTruckFreight;
  late TextEditingController
      totalFreightController; // Simple controller - user enters this
  late TextEditingController weightInTonsController;
  RxDouble truck = 0.0.obs;

  // Rx data for dropdowns and externally controlled fields

  RxList<String> invoiceTasNumberList = <String>[].obs;
  RxList<String> invoiceBiltyNumberList = <String>[].obs;
  // Rx data for dropdowns and externally controlled fields
  var voucherStatusSelected = VoucherStatus.pending.value.obs;

  // New controllers and variables
  late TextEditingController brokerFeesController;

  late TextEditingController munshianaFeesController;
  late TextEditingController companyFreightController;

  var brokerType = BrokerType.own.value.obs;
  var selectedBroker = ''.obs;
  var selectedPaymentTypes = <String>[].obs;

  // For storing selected account names
  var brokerAccount = ''.obs;
  var munshianaAccount = ''.obs;

  // Private variables to store account IDs for setting after accounts are loaded
  String? _brokerAccountIdToSet;
  String? _munshianaAccountIdToSet;

  // Payment related controllers - REMOVED CASH CONTROLLERS
  late TextEditingController dieselLitersController;
  late TextEditingController dieselCompanyController;
  late TextEditingController chequeAmountController;
  late TextEditingController chequeBankController;
  late TextEditingController chequeNumberController;

  // Broker management integration
  final RxList<BrokerModel> availableBrokers = <BrokerModel>[].obs;
  final Rx<BrokerModel?> selectedBrokerModel = Rx<BrokerModel?>(null);
  final RxList<BrokerModel> selectedBrokersList = <BrokerModel>[].obs;

  // REMOVED CASH PAYMENT OPTION - only diesel and cheque allowed
  var dieselCardSelected = false.obs;
  var chequeSelected = false.obs;

  // Show/hide dialogs
  var showPayeeDialog = false.obs;
  var showAccountDialog = false.obs;
  var showBrokerDialog = false.obs;
  var isAddingPayee = false.obs;
  var isAddingAccount = false.obs;
  var isAddingBroker = false.obs;

  late TextEditingController bankNameController;

  final dieselCompanies = ['Rajput', 'Ahad Ad co', 'Ali'];

  // Add this observable for the calculated truck freight
  RxDouble calculatedTotalTruckFreight = 0.0.obs;

  // Add this observable for the calculated total freight (truck + broker + munshiana)
  RxDouble calculatedTotalFreight = 0.0.obs;

  // Auto-calculated fields
  RxDouble calculatedSalesTax = 0.0.obs;
  RxDouble calculatedFreightTax =
      0.0.obs; // 15% tax on Company Freight amount (for information only)
  RxDouble calculatedProfit = 0.0.obs;
  RxDouble calculatedNetAmount = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    // Initialize TextEditingControllers with currentInvoice values or empty strings
    voucherNumberController = TextEditingController(
        text: currentVoucher?.voucherNumber.toString() ?? '');
    voucherStatusSelected.value =
        currentVoucher?.voucherStatus ?? VoucherStatus.pending.value;
    departureDateController = TextEditingController(
        text: currentVoucher?.departureDate ?? formatDate(DateTime.now()));

    // Product name is initialized as-is, since it's already in the right format
    productNameController =
        TextEditingController(text: currentVoucher?.productName ?? '');

    totalNumberOfBagsController = TextEditingController(
        text: currentVoucher?.totalNumberOfBags.toString() ?? '');
    brokerNameController =
        TextEditingController(text: currentVoucher?.brokerName ?? '');
    driverNameController =
        TextEditingController(text: currentVoucher?.driverName ?? '');
    driverPhoneNumberController =
        TextEditingController(text: currentVoucher?.driverPhoneNumber ?? '');

    truckNumberController =
        TextEditingController(text: currentVoucher?.truckNumber ?? '');

    conveyNoteNumberController =
        TextEditingController(text: currentVoucher?.conveyNoteNumber ?? '');

    totalTruckFreight = TextEditingController(
        text: currentVoucher?.totalFreight.toString() ?? '');

    // Initialize totalFreightController - user enters this value
    final initialTotalFreight = (currentVoucher?.totalFreight ?? 0.0) +
        (currentVoucher?.brokerFees ?? 0.0) +
        (currentVoucher?.munshianaFees ?? 0.0);

    totalFreightController =
        TextEditingController(text: initialTotalFreight.toString());

    weightInTonsController = TextEditingController(
        text: currentVoucher?.weightInTons.toString() ?? '');

    // Initialize broker and munshiana fees controllers with existing values
    brokerFeesController = TextEditingController(
        text:
            currentVoucher?.brokerFees != null && currentVoucher!.brokerFees > 0
                ? currentVoucher!.brokerFees.toString()
                : '');
    munshianaFeesController = TextEditingController(
        text: currentVoucher?.munshianaFees != null &&
                currentVoucher!.munshianaFees > 0
            ? currentVoucher!.munshianaFees.toString()
            : '');

    // Initialize Rx values with currentInvoice data if available
    currentVoucher == null ? editMode = false : editMode = true;

    currentVoucher?.invoiceTasNumberList == null
        ? invoiceTasNumberList.addAll(companyController.company.invoices
            .where((InvoiceModel invoice) =>
                invoice.conveyNoteNumber == conveyNoteNumberController.text)
            .map((InvoiceModel invoice) => invoice.tasNumber))
        : invoiceTasNumberList.addAll(currentVoucher!.invoiceTasNumberList);

    // Initialize bilty numbers list
    if (currentVoucher?.invoiceBiltyNumberList != null) {
      invoiceBiltyNumberList.addAll(currentVoucher!.invoiceBiltyNumberList);
    } else {
      // For new vouchers or vouchers without bilty numbers, extract from linked invoices
      // Use convey note numbers to find linked invoices, not TAS numbers
      final conveyNotes = invoiceTasNumberList.isNotEmpty
          ? invoiceTasNumberList
          : [conveyNoteNumberController.text]
              .where((note) => note.isNotEmpty)
              .toList();

      final linkedInvoices = companyController.company.invoices
          .where((invoice) => conveyNotes.contains(invoice.conveyNoteNumber))
          .toList();
      final biltyNumbers = linkedInvoices
          .map((invoice) => invoice.biltyNumber)
          .where((bilty) => bilty.isNotEmpty)
          .toSet()
          .toList();
      invoiceBiltyNumberList.addAll(biltyNumbers);
    }

    // Initialize the tag controller
    ensureTagControllerInitialized();

    // Initialize company freight controller
    companyFreightController = TextEditingController(
        text: currentVoucher?.companyFreight.toString() ?? '');

    // Set broker type and account selections for edit mode
    if (currentVoucher != null) {
      brokerType.value = currentVoucher!.brokerType;

      // Set broker account if available - will be set after accounts are loaded
      if (currentVoucher!.brokerAccount.isNotEmpty) {
        _brokerAccountIdToSet = currentVoucher!.brokerAccount;
      }

      // Set munshiana account if available - will be set after accounts are loaded
      if (currentVoucher!.munshianaAccount.isNotEmpty) {
        _munshianaAccountIdToSet = currentVoucher!.munshianaAccount;
      }

      // Initialize selected tax authorities for edit mode
      if (currentVoucher!.selectedTaxAuthorities.isNotEmpty) {
        selectedTaxAuthorities.addAll(currentVoucher!.selectedTaxAuthorities);
      }

      // Set broker name and payee for outsource broker type
      if (currentVoucher!.brokerType == BrokerType.outsource.value &&
          currentVoucher!.brokerName.isNotEmpty) {
        // Set broker name controller immediately
        brokerNameController.text = currentVoucher!.brokerName;

        // Find and set the payee after payees are loaded
        Future.delayed(Duration(milliseconds: 500), () {
          final payee = payees
              .firstWhereOrNull((p) => p.name == currentVoucher!.brokerName);
          if (payee != null) {
            selectedPayee.value = payee;
          }
        });

        // Find and set the broker model after brokers are loaded
        Future.delayed(Duration(milliseconds: 600), () {
          final broker = availableBrokers
              .firstWhereOrNull((b) => b.name == currentVoucher!.brokerName);
          if (broker != null) {
            selectedBrokerModel.value = broker;
            log('Set selectedBrokerModel for outsource broker: ${broker.name}');
          }
        });
      } else if (currentVoucher!.brokerType == BrokerType.own.value &&
          currentVoucher!.brokerName.isNotEmpty) {
        // For own broker, set the selected broker name and controller
        selectedBroker.value = currentVoucher!.brokerName;
        brokerNameController.text = currentVoucher!.brokerName;

        // Find and set the broker model after brokers are loaded
        Future.delayed(Duration(milliseconds: 600), () {
          final broker = availableBrokers
              .firstWhereOrNull((b) => b.name == currentVoucher!.brokerName);
          if (broker != null) {
            selectedBrokerModel.value = broker;
            log('Set selectedBrokerModel for own broker: ${broker.name}');
          }
        });
      }

      // Load broker list if available
      if (currentVoucher!.brokerList.isNotEmpty) {
        // Will be populated after brokers are loaded
        Future.delayed(const Duration(milliseconds: 500), () {
          for (String brokerId in currentVoucher!.brokerList) {
            final broker =
                availableBrokers.firstWhereOrNull((b) => b.id == brokerId);
            if (broker != null &&
                !selectedBrokersList.any((b) => b.id == broker.id)) {
              selectedBrokersList.add(broker);

              // Set the first broker as the selected broker model if not already set
              if (selectedBrokerModel.value == null) {
                selectedBrokerModel.value = broker;
                log('Set selectedBrokerModel from broker list: ${broker.name}');
              }
            }
          }
        });
      }

      // Set calculated values
      calculatedProfit.value = currentVoucher!.calculatedProfit;
      calculatedSalesTax.value = currentVoucher!.calculatedTax;
      calculatedFreightTax.value = currentVoucher!.calculatedFreightTax;

      // Set account selections for tax and profit (will be set after accounts are loaded)
      if (currentVoucher!.salesTaxAccountId != null &&
          currentVoucher!.salesTaxAccountId!.isNotEmpty) {
        Future.delayed(const Duration(milliseconds: 500), () {
          final taxAccount = accounts.firstWhereOrNull(
              (a) => a.id == currentVoucher!.salesTaxAccountId);
          if (taxAccount != null) {
            selectedSalesTaxAccount.value = taxAccount;
          }
        });
      }

      if (currentVoucher!.profitAccountId != null &&
          currentVoucher!.profitAccountId!.isNotEmpty) {
        Future.delayed(const Duration(milliseconds: 500), () {
          final profitAccount = accounts
              .firstWhereOrNull((a) => a.id == currentVoucher!.profitAccountId);
          if (profitAccount != null) {
            selectedProfitAccount.value = profitAccount;
          }
        });
      }
    }

    // REMOVED CASH CONTROLLERS - only fuel and cheque
    dieselLitersController = TextEditingController();
    dieselCompanyController = TextEditingController();
    chequeAmountController = TextEditingController();
    chequeBankController = TextEditingController();
    chequeNumberController = TextEditingController();
    bankNameController = TextEditingController();

    // Set initial date only for new vouchers (not in edit mode)
    if (currentVoucher == null) {
      departureDateController.text = formatDate(DateTime.now());
    }

    // CRITICAL FIX: Initialize payment-related data properly
    if (currentVoucher != null) {
      // Set settled freight amount
      settledFreight.value = currentVoucher?.settledFreight ?? 0.0;

      // Load existing payments into separate list for edit mode
      if (currentVoucher!.paymentTransactions.isNotEmpty) {
        for (var txn in currentVoucher!.paymentTransactions) {
          try {
            final transaction = PaymentTransactionModel.fromMap(txn);
            existingPaymentTransactions.add(transaction);
          } catch (e) {
            log('Error converting payment transaction: $e');
          }
        }
      }
    }

    // Load payees, accounts, fuel cards, and companies
    loadPayees();
    loadBrokers();
    loadAccounts();
    loadCompanies();

    // Initialize fuel card controller
    if (Get.isRegistered<FuelCardController>()) {
      fuelCardController = Get.find<FuelCardController>();
    } else {
      // If not registered (unlikely but as a fallback), create dummy data
      loadFuelCards();
    }

    // Add listeners to update truck freight when user changes values
    totalFreightController.addListener(updateTruckFreight);
    brokerFeesController.addListener(updateTruckFreight);
    munshianaFeesController.addListener(updateTruckFreight);
    companyFreightController.addListener(performAutoCalculations);

    // Initial calculation
    updateTruckFreight();

    // Initialize Chart of Accounts controller
    _initializeChartOfAccountsController();
  }

  /// Initialize Chart of Accounts controller
  void _initializeChartOfAccountsController() async {
    try {
      log('VoucherController: Attempting to find ChartOfAccountsController...');
      final chartController = Get.find<ChartOfAccountsController>();
      log('VoucherController: ChartOfAccountsController found successfully');

      // Ensure accounts are loaded for dropdown usage
      log('VoucherController: Current accounts count: ${chartController.accounts.length}');
      if (chartController.accounts.isEmpty) {
        log('VoucherController: Chart of Accounts list is empty, loading accounts...');
        await chartController.loadAllAccountsForDropdown();

        // Check if still empty after loading
        if (chartController.accounts.isEmpty) {
          log('VoucherController: No Chart of Accounts found after loading. User may need to create accounts or generate mock data.');
          SnackbarUtils.showInfo('Chart of Accounts',
              'No accounts found. Please create accounts in the Chart of Accounts section or generate mock data for testing.');

          // Offer to create mock data
          _showCreateAccountsDialog();
        } else {
          log('VoucherController: Successfully loaded ${chartController.accounts.length} accounts for dropdown usage');

          // Debug: Log account categories
          final categories = chartController.accounts
              .map((a) => a.accountCategory.displayName)
              .toSet();
          log('VoucherController: Available account categories: ${categories.join(', ')}');
        }
      } else {
        log('VoucherController: Chart of Accounts already loaded: ${chartController.accounts.length} accounts');

        // Debug: Log account categories
        final categories = chartController.accounts
            .map((a) => a.accountCategory.displayName)
            .toSet();
        log('VoucherController: Available account categories: ${categories.join(', ')}');
      }
    } catch (e) {
      // Controller not found, it should be registered in app_bindings.dart
      log('ChartOfAccountsController not found: $e');
      log('Make sure ChartOfAccountsController is registered in app_bindings.dart');
    }
  }

  /// Show dialog to create Chart of Accounts
  void _showCreateAccountsDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('No Chart of Accounts Found'),
        content: const Text(
          'No Chart of Accounts have been created yet. You can either:\n\n'
          '1. Create accounts manually in the Chart of Accounts section\n'
          '2. Generate sample accounts for testing\n\n'
          'Would you like to generate sample accounts now?',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _generateMockAccounts();
            },
            child: const Text('Generate Sample Accounts'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // Navigate to Chart of Accounts screen
              Get.toNamed('/chart-of-accounts');
            },
            child: const Text('Go to Chart of Accounts'),
          ),
        ],
      ),
    );
  }

  /// Generate mock Chart of Accounts data
  Future<void> _generateMockAccounts() async {
    try {
      SnackbarUtils.showInfo(
          'Generating Accounts', 'Creating sample Chart of Accounts...');

      final mockDataService = Get.find<MockDataService>();
      final uid = FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';
      final success = await mockDataService.createLogisticsMockData(uid);

      if (success) {
        SnackbarUtils.showSuccess(
            'Success', 'Sample accounts created successfully!');

        // Reload Chart of Accounts
        final chartController = Get.find<ChartOfAccountsController>();
        await chartController.loadAllAccountsForDropdown();

        log('VoucherController: Mock data generated, reloaded ${chartController.accounts.length} accounts');
      } else {
        SnackbarUtils.showError('Error', 'Failed to generate sample accounts');
      }
    } catch (e) {
      log('VoucherController: Error generating mock accounts: $e');
      SnackbarUtils.showError(
          'Error', 'Failed to generate sample accounts: $e');
    }
  }

  /// Fix account categories for manually created accounts
  Future<void> fixAccountCategories() async {
    try {
      log('=== FIXING ACCOUNT CATEGORIES ===');

      final chartController = Get.find<ChartOfAccountsController>();
      await chartController.loadAllAccountsForDropdown();

      final accountsToFix = <String, Map<String, dynamic>>{
        // Liability Accounts (Money we owe to others)
        'Broker Fees': {
          'category': AccountCategory.liabilities,
          'type': AccountType.accountsPayable,
        },
        'Truck Fare': {
          'category': AccountCategory.liabilities,
          'type': AccountType.accountsPayable,
        },
        '15% Sales Tax': {
          'category': AccountCategory.liabilities,
          'type': AccountType.accountsPayable,
        },
        '6.9 WHT tax': {
          'category': AccountCategory.liabilities,
          'type': AccountType.accountsPayable,
        },

        // Asset Accounts (Money owed to us)
        'Company Fare NLC': {
          'category': AccountCategory.assets,
          'type': AccountType.accountsReceivable,
        },

        // Equity Accounts (Our earnings and ownership)
        'Mushina': {
          'category': AccountCategory.equity,
          'type': AccountType.retainedEarnings,
        },
        'Net': {
          'category': AccountCategory.equity,
          'type': AccountType.retainedEarnings,
        },
      };

      int fixedCount = 0;

      for (var account in chartController.accounts) {
        if (accountsToFix.containsKey(account.accountName)) {
          final accountFix = accountsToFix[account.accountName]!;
          final correctCategory = accountFix['category'] as AccountCategory;
          final correctType = accountFix['type'] as AccountType;

          if (account.category != correctCategory ||
              account.accountType != correctType) {
            log('Fixing ${account.accountName}: ${account.category.displayName}/${account.accountType.displayName} → ${correctCategory.displayName}/${correctType.displayName}');

            final updatedAccount = account.copyWith(
              category: correctCategory,
              accountType: correctType,
              updatedAt: DateTime.now(),
            );

            final result =
                await chartController.repository.updateAccount(updatedAccount);
            result.fold(
              (failure) => log(
                  'Failed to fix ${account.accountName}: ${failure.message}'),
              (success) {
                log('Successfully fixed ${account.accountName}');
                fixedCount++;
              },
            );
          }
        }
      }

      if (fixedCount > 0) {
        // Reload accounts after fixes
        await chartController.loadAllAccountsForDropdown();
        SnackbarUtils.showSuccess(
            'Success', 'Fixed $fixedCount account categories');
        log('=== ACCOUNT CATEGORIES FIXED ===');
      } else {
        SnackbarUtils.showInfo('Info', 'No account categories needed fixing');
      }
    } catch (e) {
      log('Error fixing account categories: $e');
      SnackbarUtils.showError('Error', 'Failed to fix account categories: $e');
    }
  }

  /// Debug Chart of Accounts data loading
  void debugChartOfAccounts() async {
    try {
      log('=== DEBUG CHART OF ACCOUNTS ===');

      final chartController = Get.find<ChartOfAccountsController>();
      log('Chart Controller found: true');
      log('Current accounts count: ${chartController.accounts.length}');

      // Force reload
      log('Force reloading accounts...');
      await chartController.loadAllAccountsForDropdown();
      log('After reload - accounts count: ${chartController.accounts.length}');

      // Log account details
      if (chartController.accounts.isNotEmpty) {
        log('=== ACCOUNT DETAILS ===');
        for (var account in chartController.accounts.take(10)) {
          log('Account: ${account.accountNumber} - ${account.accountName} (${account.accountCategory.displayName}) [UID: ${account.uid}] [Active: ${account.isActive}]');
        }

        // Log categories
        final categories = chartController.accounts
            .map((a) => a.accountCategory.displayName)
            .toSet();
        log('Available categories: ${categories.join(', ')}');

        // Log by category
        for (var category in AccountCategory.values) {
          final categoryAccounts = chartController.accounts
              .where((a) => a.accountCategory == category)
              .length;
          log('${category.displayName}: $categoryAccounts accounts');
        }

        // Test dropdown filtering
        log('=== TESTING DROPDOWN FILTERING ===');

        // Test ExpenseAccountDropdown filtering
        final expenseAccounts = chartController.accounts.where((account) {
          return account.category == AccountCategory.expenses &&
              account.isActive &&
              [
                AccountType.operatingExpenses,
                AccountType.administrativeExpenses
              ].contains(account.accountType);
        }).toList();
        log('ExpenseAccountDropdown would show: ${expenseAccounts.length} accounts');
        for (var account in expenseAccounts.take(3)) {
          log('  - ${account.accountName} (${account.accountType.displayName})');
        }

        // Test LiabilityAccountDropdown filtering
        final liabilityAccounts = chartController.accounts.where((account) {
          return account.category == AccountCategory.liabilities &&
              account.isActive &&
              [AccountType.currentLiabilities, AccountType.accountsPayable]
                  .contains(account.accountType);
        }).toList();
        log('LiabilityAccountDropdown would show: ${liabilityAccounts.length} accounts');
        for (var account in liabilityAccounts.take(3)) {
          log('  - ${account.accountName} (${account.accountType.displayName})');
        }

        // Test AssetAccountDropdown filtering (NLC Receivables)
        final assetAccounts = chartController.accounts.where((account) {
          return account.category == AccountCategory.assets &&
              account.isActive &&
              [
                AccountType.cash,
                AccountType.bank,
                AccountType.currentAssets,
                AccountType.accountsReceivable
              ].contains(account.accountType);
        }).toList();
        log('AssetAccountDropdown would show: ${assetAccounts.length} accounts');
        for (var account in assetAccounts.take(5)) {
          log('  - ${account.accountName} (${account.accountType.displayName})');
        }

        // Test EquityAccountDropdown filtering (Munshiana, Net Profit)
        final equityAccounts = chartController.accounts.where((account) {
          return account.category == AccountCategory.equity &&
              account.isActive &&
              [AccountType.retainedEarnings, AccountType.ownersEquity]
                  .contains(account.accountType);
        }).toList();
        log('EquityAccountDropdown would show: ${equityAccounts.length} accounts');
        for (var account in equityAccounts.take(5)) {
          log('  - ${account.accountName} (${account.accountType.displayName})');
        }
      } else {
        log('No accounts found - showing create dialog');
        _showCreateAccountsDialog();
      }

      log('=== END DEBUG ===');

      SnackbarUtils.showInfo(
          'Debug', 'Check console for Chart of Accounts debug info');
    } catch (e) {
      log('Debug error: $e');
      SnackbarUtils.showError('Debug Error', e.toString());
    }
  }

  void updateTruckFreight() {
    // Get values from controllers
    final totalFreight = double.tryParse(totalFreightController.text) ?? 0.0;
    final brokerFees = double.tryParse(brokerFeesController.text) ?? 0.0;
    final munshiana = double.tryParse(munshianaFeesController.text) ?? 0.0;

    // Calculate truck freight: totalFreight - (brokerFees + munshiana)
    final truckFreight = totalFreight - (brokerFees + munshiana);

    // Update the truck freight controller text
    final newText = truckFreight.toStringAsFixed(0);
    if (totalTruckFreight.text != newText) {
      totalTruckFreight.text = newText;
    }

    // Update reactive variables for other calculations
    calculatedTotalTruckFreight.value = truckFreight;
    calculatedTotalFreight.value = totalFreight;

    // Trigger auto-calculations
    performAutoCalculations();
  }

  void performAutoCalculations() {
    final companyFreight =
        double.tryParse(companyFreightController.text) ?? 0.0;

    // Calculate 4.6% sales tax on company freight
    final salesTax = companyFreight > 0 ? companyFreight * 0.046 : 0.0;
    calculatedSalesTax.value = salesTax;

    // Calculate 15% tax on Company Freight amount (for display only)
    final companyFreightTax = companyFreight > 0 ? companyFreight * 0.15 : 0.0;
    calculatedFreightTax.value = companyFreightTax;

    // Calculate profit: company freight - sales tax - truck freight (excluding company freight tax)
    final truckFreight = calculatedTotalTruckFreight.value;
    final profit = companyFreight - salesTax - truckFreight;
    calculatedProfit.value = profit;

    // Calculate net amount after all deductions
    final netAmount = companyFreight - salesTax;
    calculatedNetAmount.value = netAmount;

    update();
  }

  // Fetch payees from Firestore
  Future<void> loadPayees() async {
    try {
      final result = await payeeRepository.getPayees();
      result.fold(
        (failure) => (failure) => SnackbarUtils.showError(
              AppStrings.errorS,
              'Failed to load payees: ${failure.message}',
            ),
        (payeesList) {
          payees.value = payeesList;
          update();
        },
      );
    } catch (e) {
      log('Error loading payees: $e');
    }
  }

  // Fetch brokers from Firestore
  Future<void> loadBrokers() async {
    try {
      final result = await brokerRepository.getBrokers();
      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          'Failed to load brokers: $failure',
        ),
        (brokersList) {
          availableBrokers.value = brokersList;

          // Restore broker selections if editing existing voucher
          if (currentVoucher != null && currentVoucher!.brokerList.isNotEmpty) {
            selectedBrokersList.clear();
            for (String brokerId in currentVoucher!.brokerList) {
              final broker =
                  brokersList.firstWhereOrNull((b) => b.id == brokerId);
              if (broker != null &&
                  !selectedBrokersList.any((b) => b.id == broker.id)) {
                selectedBrokersList.add(broker);

                // Set the first broker as the selected broker model if not already set
                if (selectedBrokerModel.value == null) {
                  selectedBrokerModel.value = broker;
                  log('Set selectedBrokerModel from loadBrokers: ${broker.name}');
                }
              }
            }
          }

          // Also restore broker model based on broker name if not set by broker list
          if (currentVoucher != null &&
              currentVoucher!.brokerName.isNotEmpty &&
              selectedBrokerModel.value == null) {
            final broker = brokersList
                .firstWhereOrNull((b) => b.name == currentVoucher!.brokerName);
            if (broker != null) {
              selectedBrokerModel.value = broker;
              log('Set selectedBrokerModel by name from loadBrokers: ${broker.name}');
            }
          }

          update();
        },
      );
    } catch (e) {
      log('Error loading brokers: $e');
    }
  }

  // Fetch accounts from Firestore
  Future<void> loadAccounts() async {
    try {
      final result = await accountRepository.getAccounts();
      result.fold(
        (failure) => (failure) => SnackbarUtils.showError(
              AppStrings.errorS,
              'Failed to load accounts: ${failure.message}',
            ),
        (accountsList) {
          accounts.value = accountsList;

          // Set broker and munshiana accounts if IDs were stored
          if (_brokerAccountIdToSet != null) {
            final brokerAccount = accountsList.firstWhereOrNull(
                (account) => account.id == _brokerAccountIdToSet);
            if (brokerAccount != null) {
              selectedBrokerAccount.value = brokerAccount;
              log('Set broker account: ${brokerAccount.name}');
            }
            _brokerAccountIdToSet = null; // Clear after setting
          }

          if (_munshianaAccountIdToSet != null) {
            final munshianaAccount = accountsList.firstWhereOrNull(
                (account) => account.id == _munshianaAccountIdToSet);
            if (munshianaAccount != null) {
              selectedMunshianaAccount.value = munshianaAccount;
              log('Set munshiana account: ${munshianaAccount.name}');
            }
            _munshianaAccountIdToSet = null; // Clear after setting
          }

          // Restore account selections for Company Freight, Tax, and Profit if editing existing voucher
          if (currentVoucher != null) {
            // Restore Company Freight account
            if (currentVoucher!.companyFreightAccountId != null &&
                currentVoucher!.companyFreightAccountId!.isNotEmpty) {
              final companyFreightAccount = accountsList.firstWhereOrNull(
                  (account) =>
                      account.id == currentVoucher!.companyFreightAccountId);
              if (companyFreightAccount != null) {
                selectedCompanyFreightAccount.value = companyFreightAccount;
                log('Set company freight account: ${companyFreightAccount.name}');
              }
            }

            // Restore Tax account
            if (currentVoucher!.salesTaxAccountId != null &&
                currentVoucher!.salesTaxAccountId!.isNotEmpty) {
              final taxAccount = accountsList.firstWhereOrNull(
                  (account) => account.id == currentVoucher!.salesTaxAccountId);
              if (taxAccount != null) {
                selectedSalesTaxAccount.value = taxAccount;
                log('Set tax account: ${taxAccount.name}');
              }
            }

            // Restore Freight Tax account
            if (currentVoucher!.freightTaxAccountId != null &&
                currentVoucher!.freightTaxAccountId!.isNotEmpty) {
              final freightTaxAccount = accountsList.firstWhereOrNull(
                  (account) =>
                      account.id == currentVoucher!.freightTaxAccountId);
              if (freightTaxAccount != null) {
                selectedFreightTaxAccount.value = freightTaxAccount;
                log('Set freight tax account: ${freightTaxAccount.name}');
              }
            }

            // Restore Profit account
            if (currentVoucher!.profitAccountId != null &&
                currentVoucher!.profitAccountId!.isNotEmpty) {
              final profitAccount = accountsList.firstWhereOrNull(
                  (account) => account.id == currentVoucher!.profitAccountId);
              if (profitAccount != null) {
                selectedProfitAccount.value = profitAccount;
                log('Set profit account: ${profitAccount.name}');
              }
            }
          }

          update();
        },
      );
    } catch (e) {
      log('Error loading accounts: $e');
    }
  }

  // Add a new payee
  Future<void> addPayee() async {
    if (!payeeFormKey.currentState!.validate()) {
      return;
    }

    isAddingPayee.value = true;
    update();

    try {
      final newPayee = PayeeModel(
        id: const Uuid().v4(),
        name: payeeNameController.text,
        phoneNumber: payeePhoneController.text,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = await payeeRepository.createPayee(newPayee);
      result.fold(
        (failure) => (failure) => SnackbarUtils.showError(
              AppStrings.errorS,
              failure.message,
            ),
        (success) {
          payees.add(newPayee);
          selectedPayee.value = newPayee;
          brokerNameController.text = newPayee.name;
          showPayeeDialog.value = false;
          clearPayeeForm();
          update();
          SnackbarUtils.showSuccess(
            AppStrings.success,
            success.message,
          );
        },
      );
    } catch (e) {
      (failure) => SnackbarUtils.showError(
            AppStrings.errorS,
            'Failed to add payee: $e',
          );
    } finally {
      isAddingPayee.value = false;
      update();
    }
  }

  // Add a new account
  Future<void> addAccount(bool isForBroker) async {
    if (!accountFormKey.currentState!.validate()) {
      return;
    }

    isAddingAccount.value = true;
    update();

    try {
      final newAccount = AccountModel(
        id: const Uuid().v4(),
        name: accountNameController.text,
        initialBalance: double.tryParse(initialBalanceController.text) ?? 0.0,
        accountNumber: accountNumberController.text,
        branchCode: branchCodeController.text,
        branchAddress: branchAddressController.text,
        availableBalance: double.tryParse(initialBalanceController.text) ?? 0.0,
        createdAt: DateTime.now(),
      );

      final result = await accountRepository.createAccount(newAccount);
      result.fold(
        (failure) => (failure) => SnackbarUtils.showError(
              AppStrings.errorS,
              failure.message,
            ),
        (success) {
          accounts.add(newAccount);
          if (isForBroker) {
            selectedBrokerAccount.value = newAccount;
          } else {
            selectedMunshianaAccount.value = newAccount;
          }
          showAccountDialog.value = false;
          clearAccountForm();
          update();
          SnackbarUtils.showSuccess(
            AppStrings.success,
            success.message,
          );
        },
      );
    } catch (e) {
      (failure) => SnackbarUtils.showError(
            AppStrings.errorS,
            'Failed to add account: $e',
          );
    } finally {
      isAddingAccount.value = false;
      update();
    }
  }

  // Clear payee form fields
  void clearPayeeForm() {
    payeeNameController.clear();
    payeePhoneController.clear();
  }

  // Clear account form fields
  void clearAccountForm() {
    accountNameController.clear();
    accountNumberController.clear();
    initialBalanceController.clear();
    branchCodeController.clear();
    branchAddressController.clear();
  }

  // Validation for payee form
  String? validatePayeeName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    return null;
  }

  String? validatePayeePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    if (!RegExp(r'^03[0-9]{9}$').hasMatch(value)) {
      return 'Phone number must be 11 digits starting with 03';
    }
    return null;
  }

  // Validation for account form
  String? validateAccountName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    return null;
  }

  String? validateAccountNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Account number is required';
    }
    return null;
  }

  String? validateInitialBalance(String? value) {
    if (value == null || value.isEmpty) {
      return 'Initial balance is required';
    }
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    return null;
  }

  String? validateBranchCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Branch code is required';
    }
    return null;
  }

  // Tax Authority Methods - Ultra-fast implementation
  void toggleTaxAuthority(String authority) {
    // Pre-calculate state for maximum speed
    final isCurrentlySelected = selectedTaxAuthorities.contains(authority);

    if (isCurrentlySelected) {
      // Immediate deselection - no checks needed
      selectedTaxAuthorities.remove(authority);
      taxAuthorityError.value = null; // Always clear error on deselection
    } else {
      // Fast selection with immediate limit check
      if (selectedTaxAuthorities.length >= 2) {
        taxAuthorityError.value =
            'You can select a maximum of 2 tax authorities';
        return; // Exit immediately
      }
      selectedTaxAuthorities.add(authority);
      taxAuthorityError.value = null; // Clear any existing error
    }

    // Force immediate UI update for zero-delay experience
    update(['tax_authorities']);
  }

  bool isTaxAuthoritySelected(String authority) {
    return selectedTaxAuthorities.contains(authority);
  }

  void clearTaxAuthorityError() {
    taxAuthorityError.value = null;
  }

  void setSelectedPayee(PayeeModel? payee) {
    selectedPayee.value = payee;
    if (payee != null) {
      brokerNameController.text = payee.name;
    }
    update();
  }

  void setSelectedBrokerAccount(AccountModel? account) {
    selectedBrokerAccount.value = account;
    update();
  }

  void setSelectedMunshianaAccount(AccountModel? account) {
    selectedMunshianaAccount.value = account;
    update();
  }

  void setSelectedCompanyFreightAccount(AccountModel? account) {
    selectedCompanyFreightAccount.value = account;
    update();
  }

  void setSelectedSalesTaxAccount(AccountModel? account) {
    selectedSalesTaxAccount.value = account;
    update();
  }

  void setSelectedFreightTaxAccount(AccountModel? account) {
    selectedFreightTaxAccount.value = account;
    update();
  }

  void setSelectedProfitAccount(AccountModel? account) {
    selectedProfitAccount.value = account;
    update();
  }

  void setSelectedBrokerCompany(UserModel? company) {
    selectedBrokerCompany.value = company;
    update();
  }

  // Load companies for Own broker type
  Future<void> loadCompanies() async {
    try {
      final companyService = CompanyFirebaseService();
      final result = await companyService.getAllUsers();
      result.fold(
        (failure) => log('Failed to load companies: ${failure.message}'),
        (companies) {
          availableCompanies.value = companies;

          // Restore broker company selection if editing existing voucher
          if (currentVoucher != null &&
              currentVoucher!.brokerCompanyId != null &&
              currentVoucher!.brokerCompanyId!.isNotEmpty) {
            final brokerCompany = companies.firstWhereOrNull(
                (company) => company.uid == currentVoucher!.brokerCompanyId);
            if (brokerCompany != null) {
              selectedBrokerCompany.value = brokerCompany;
              log('Set broker company: ${brokerCompany.companyName}');
            }
          }

          update();
        },
      );
    } catch (e) {
      log('Error loading companies: $e');
    }
  }

  // Broker management methods
  void setSelectedBroker(BrokerModel? broker) {
    selectedBrokerModel.value = broker;
    if (broker != null) {
      // Add to selected brokers list if not already present
      if (!selectedBrokersList.any((b) => b.id == broker.id)) {
        selectedBrokersList.add(broker);
      }
    }
    update();
  }

  void removeBrokerFromList(BrokerModel broker) {
    selectedBrokersList.removeWhere((b) => b.id == broker.id);
    if (selectedBrokerModel.value?.id == broker.id) {
      selectedBrokerModel.value = null;
    }
    update();
  }

  void openBrokerDialog() {
    showBrokerDialog.value = true;
  }

  void closeBrokerDialog() {
    showBrokerDialog.value = false;
  }

  Future<void> createBroker({
    required String name,
    String? description,
    String? phoneNumber,
    String? email,
    String? address,
  }) async {
    try {
      isAddingBroker.value = true;

      final broker = BrokerModel(
        id: '', // Will be set by Firebase
        name: name,
        description: description,
        phoneNumber: phoneNumber,
        email: email,
        address: address,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uid: '', // Will be set by Firebase service
      );

      final result = await brokerRepository.createBroker(broker);

      result.fold(
        (failure) => SnackbarUtils.showError(
          AppStrings.errorS,
          'Failed to create broker: ${failure.message}',
        ),
        (_) {
          SnackbarUtils.showSuccess(
            'Success',
            'Broker created successfully',
          );
          closeBrokerDialog();
          loadBrokers(); // Refresh the broker list
        },
      );
    } catch (e) {
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Error creating broker: $e',
      );
    } finally {
      isAddingBroker.value = false;
    }
  }

  void openPayeeDialog() {
    showPayeeDialog.value = true;
    update();
  }

  void closePayeeDialog() {
    showPayeeDialog.value = false;
    update();
  }

  void openAccountDialog(bool isForBroker) {
    showAccountDialog.value = true;
    update();
  }

  void closeAccountDialog() {
    showAccountDialog.value = false;
    update();
  }

  selectDepartureDate(BuildContext context) async {
    var date = await selectDate(context);
    if (date == null) {
      // orderDateController.text=formatDate(DateTime(0,0,0));
    } else {
      departureDateController.text = formatDate(date);
    }
  }

  String formatDate(DateTime date) {
    // Format the day, month, and year with padding for single-digit numbers
    String day = date.day.toString().padLeft(2, '0');
    String month = date.month.toString().padLeft(2, '0');
    String year = date.year.toString();

    // Combine into the desired format
    return '$day/$month/$year';
  }

  // Helper method to parse departure date from voucher
  DateTime parseDepartureDate(String departureDateString) {
    try {
      // Parse date in DD/MM/YYYY format
      List<String> parts = departureDateString.split('/');
      if (parts.length == 3) {
        int day = int.parse(parts[0]);
        int month = int.parse(parts[1]);
        int year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      log('Error parsing departure date: $e');
    }
    // Fallback to current date if parsing fails
    return DateTime.now();
  }

  @override
  void onClose() {
    // Mark the tags as uninitialized
    tagsInitialized.value = false;

    try {
      // Dispose the tag controller
      stringTagController.dispose();
    } catch (e) {
      // Ignore errors if already disposed
    }

    voucherNumberController.dispose();
    departureDateController.dispose();
    productNameController.dispose();
    totalNumberOfBagsController.dispose();

    brokerNameController.dispose();
    driverNameController.dispose();
    driverPhoneNumberController.dispose();

    truckNumberController.dispose();
    conveyNoteNumberController.dispose();

    totalTruckFreight.dispose();
    totalFreightController.dispose();
    weightInTonsController.dispose();

    brokerFeesController.dispose();
    munshianaFeesController.dispose();
    companyFreightController.dispose();
    dieselLitersController.dispose();
    bankNameController.dispose();
    chequeNumberController.dispose();
    chequeAmountController.dispose();

    // Dispose of controllers for adding payee/account
    payeeNameController.dispose();
    payeePhoneController.dispose();
    accountNameController.dispose();
    accountNumberController.dispose();
    initialBalanceController.dispose();
    branchCodeController.dispose();
    branchAddressController.dispose();

    // Remove listeners
    totalFreightController.removeListener(updateTruckFreight);
    brokerFeesController.removeListener(updateTruckFreight);
    munshianaFeesController.removeListener(updateTruckFreight);

    super.onClose();
  }

  Future<DateTime?> selectDate(BuildContext context) async {
    notifier = Provider.of(context, listen: false);
    var date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            datePickerTheme: DatePickerThemeData(
              backgroundColor: notifier.getBgColor,
            ),
            colorScheme: ColorScheme.light(
              onSurface: notifier.text,
              surface: notifier.getBgColor,
              primary: const Color(0xFF2B79F3),
            ), // body text color
          ),
          child: child!,
        );
      },
      firstDate: DateTime(2020), // Allow dates from 2020 onwards
    );
    return date;
  }

  String? validateVoucherNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Voucher number is required.';
    }
    if (!RegExp(r'^\d+$').hasMatch(value)) {
      return 'Voucher number must be numeric.';
    }

    // Check for duplicate voucher number
    if (!editMode &&
        companyController.company.vouchers
            .any((v) => v.voucherNumber == value)) {
      return 'Voucher number already exists.';
    }

    return null;
  }

  String? validateProductName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Product name is required.';
    }
    return null;
  }

  String? validateTotalNumberOfBags(String? value) {
    if (value == null || value.isEmpty) {
      return 'Number of bags is required.';
    }
    if (int.tryParse(value) == null || int.parse(value) <= 0) {
      return 'Number of bags must be a positive integer.';
    }
    return null;
  }

  String? validateBrokerName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Broker name is required.';
    }
    return null;
  }

  String? validateDriverName(String? value) {
    // Driver name is now optional - only validate format if provided
    if (value != null && value.trim().isNotEmpty) {
      if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(value)) {
        return 'Driver name can only contain alphabets.';
      }
    }
    return null;
  }

  String? validateDriverPhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Driver phone number is required.';
    }
    if (!RegExp(r'^03[0-9]{9}$').hasMatch(value)) {
      return 'Phone number must be 11 digits starting with 03.';
    }
    return null;
  }

  String? validateTruckNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Truck number is required.';
    }
    if (!RegExp(r'^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9]+$').hasMatch(value)) {
      return 'Truck number must contain both letters and numbers.';
    }
    return null;
  }

  String? validateConveyNoteNumber(String? value) {
    log('validator convey called');
    if (value == null || value.isEmpty) {
      return 'Convey note number is required.';
    }
    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
      return 'Convey note number must be alphanumeric.';
    }

    // Check if this convey note exists in any invoice
    final matchingInvoices = companyController.company.invoices
        .where((invoice) => invoice.conveyNoteNumber == value)
        .toList();

    if (matchingInvoices.isEmpty) {
      showConveyNoteError(value);
      return 'Invalid convey note number. Not found in any invoice.';
    }

    return null;
  }

  // Show error for invalid convey note
  void showConveyNoteError(String conveyNote) {
    (failure) => SnackbarUtils.showError(
          "Invalid Convey Note",
          "The Convey Note Number you are trying to add ($conveyNote) is not linked with any existing invoice. Please check and enter a valid number.",
        );
  }

  // Add method for tag submission validation
  bool validateConveyNoteTag(String tag) {
    // Check if the tag already exists to prevent duplicates
    List<String> existingTags = stringTagController.getTags;
    if (existingTags.contains(tag)) {
      (failure) => SnackbarUtils.showError(
            "Duplicate Convey Note",
            "The Convey Note Number ($tag) has already been added.",
          );
      return false;
    }

    // Check if the convey note exists in any invoice
    final matchingInvoices = companyController.company.invoices
        .where((invoice) => invoice.conveyNoteNumber == tag)
        .toList();

    if (matchingInvoices.isEmpty) {
      showConveyNoteError(tag);
      return false;
    }

    // Update products, bags and tons when a valid tag is added
    updateProductDetails();

    return true;
  }

  // Add method to update product, total bags, and tons from linked invoices
  void updateProductDetails() {
    // Get all convey note numbers from tags
    List<String> conveyNotes = stringTagController.getTags;

    if (conveyNotes.isEmpty) {
      // Clear fields if no convey notes
      productNameController.text = '';
      totalNumberOfBagsController.text = '';
      weightInTonsController.text = '';
      truckNumberController.text = ''; // CLEAR TRUCK NUMBER TOO
      invoiceBiltyNumberList.clear(); // CLEAR BILTY NUMBERS TOO
      return;
    }

    // Find all linked invoices
    final linkedInvoices = companyController.company.invoices
        .where((invoice) => conveyNotes.contains(invoice.conveyNoteNumber))
        .toList();

    if (linkedInvoices.isEmpty) {
      // Clear fields if no matching invoices
      productNameController.text = '';
      totalNumberOfBagsController.text = '';
      weightInTonsController.text = '';
      truckNumberController.text = ''; // CLEAR TRUCK NUMBER TOO
      invoiceBiltyNumberList.clear(); // CLEAR BILTY NUMBERS TOO
      return;
    }

    // Extract unique product names
    Set<String> uniqueProducts = {};
    Set<String> uniqueTruckNumbers = {}; // NEW: Extract truck numbers
    Set<String> uniqueBiltyNumbers = {}; // NEW: Extract bilty numbers
    int totalBags = 0;
    double totalWeightInTons = 0;

    for (var invoice in linkedInvoices) {
      uniqueProducts.add(invoice.productName);

      // NEW: Add truck number from invoice
      if (invoice.truckNumber.isNotEmpty) {
        uniqueTruckNumbers.add(invoice.truckNumber);
      }

      // NEW: Add bilty number from invoice
      if (invoice.biltyNumber.isNotEmpty) {
        uniqueBiltyNumbers.add(invoice.biltyNumber);
      }

      totalBags += invoice.numberOfBags;
      // Convert weight from bags to tons
      double weightInTons = (invoice.numberOfBags * invoice.weightPerBag) /
          1000; // assuming weight is in kg
      totalWeightInTons += weightInTons;
    }

    // Format product names as comma-separated list
    productNameController.text = uniqueProducts.join(", ");

    // NEW: Auto-fill truck numbers
    if (uniqueTruckNumbers.isNotEmpty) {
      truckNumberController.text = uniqueTruckNumbers.join(", ");
    }

    // NEW: Update bilty numbers list
    invoiceBiltyNumberList.clear();
    invoiceBiltyNumberList.addAll(uniqueBiltyNumbers.toList());

    // Update total bags and tons
    totalNumberOfBagsController.text = totalBags.toString();
    weightInTonsController.text = totalWeightInTons.toStringAsFixed(2);

    update();
  }

  String? validateTotalFreight(String? value) {
    if (value == null || value.isEmpty) {
      return 'Total Freight is required.';
    }
    final totalFreight = double.tryParse(value);
    if (totalFreight == null || totalFreight <= 0) {
      return 'Total Freight must be a positive number.';
    }

    // Check if total freight is less than already settled amount
    if (totalFreight < settledFreight.value) {
      return 'Total freight (PKR ${totalFreight.toStringAsFixed(2)}) cannot be less than already paid amount (PKR ${settledFreight.value.toStringAsFixed(2)}).';
    }

    return null;
  }

  String? validateWeightInTons(String? value) {
    if (value == null || value.isEmpty) {
      return 'Weight  is required.';
    }
    if (double.tryParse(value) == null || double.parse(value) <= 0) {
      return 'Weight in Tons  must be a positive number.';
    }
    return null;
  }

  String? validateDepartureDate(String? value) {
    final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
    if (value == null || value.isEmpty) {
      return 'Please enter the Departure date.';
    }

    if (!dateRegex.hasMatch(value)) {
      return 'Invalid date format. Use DD/MM/YYYY.';
    }

    try {
      List<String> parts = value.split('/');
      int day = int.parse(parts[0]);
      int month = int.parse(parts[1]);
      int year = int.parse(parts[2]);

      if (month < 1 || month > 12) {
        return 'Invalid month.';
      }

      if (day < 1 || day > 31) {
        return 'Invalid day.';
      }

      // Handle months with less than 31 days
      if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
        return 'This month has only 30 days.';
      }

      // Handle February
      if (month == 2) {
        bool isLeapYear =
            (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
        if (day > (isLeapYear ? 29 : 28)) {
          return 'February has only ${isLeapYear ? 29 : 28} days this year.';
        }
      }

      // Allow any date for vouchers - no past date restriction
      // DateTime selectedDate = DateTime(year, month, day);
      // DateTime today = DateTime.now();
      // DateTime startOfToday = DateTime(today.year, today.month, today.day);

      // if (selectedDate.isBefore(startOfToday)) {
      //   return 'Departure date cannot be in the past.';
      // }
    } catch (e) {
      return 'Invalid date.';
    }

    return null; // Valid date
  }

  String? validateVoucherStatus(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Must select.';
    }
    return null;
  }

// Text formatters for your fields
  final voucherNumberFormatter = FilteringTextInputFormatter.digitsOnly;
  final totalNumberOfBagsFormatter = FilteringTextInputFormatter.digitsOnly;
  final phoneNumberFormatter =
      FilteringTextInputFormatter.allow(RegExp(r'[0-9]'));
  final truckNumberFormatter =
      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]'));
  final conveyNoteNumberFormatter =
      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]'));
  final totalFreightFormatter =
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.]'));
  final weightInTonsFormatter =
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.]'));
  VoucherModel? _currentVoucherBeingSaved;

  saveVoucher() async {
    // Validate broker account selection for outsource broker with fees
    if (brokerType.value == BrokerType.outsource.value &&
        brokerFeesController.text.isNotEmpty &&
        double.tryParse(brokerFeesController.text) != null &&
        double.parse(brokerFeesController.text) > 0) {
      // Check both Chart of Accounts (new) and legacy account selections
      if (selectedBrokerChartAccount.value == null &&
          selectedBrokerAccount.value == null) {
        SnackbarUtils.showError(
            'Validation Error', 'Please select an account for broker fees');
        return;
      }
    }

    // Validate munshiana account selection when munshiana fees are entered
    if (munshianaFeesController.text.isNotEmpty &&
        double.tryParse(munshianaFeesController.text) != null &&
        double.parse(munshianaFeesController.text) > 0) {
      // Check both Chart of Accounts (new) and legacy account selections
      if (selectedMunshianaChartAccount.value == null &&
          selectedMunshianaAccount.value == null) {
        SnackbarUtils.showError(
            'Validation Error', 'Please select an account for munshiana fees');
        return;
      }
    }

    // Validate financial accounting accounts if company freight > 0
    final companyFreight =
        double.tryParse(companyFreightController.text) ?? 0.0;
    if (companyFreight > 0) {
      // Check both Chart of Accounts (new) and legacy account selections for company freight
      if (selectedCompanyFreightChartAccount.value == null &&
          selectedCompanyFreightAccount.value == null) {
        SnackbarUtils.showError(
            'Validation Error', 'Please select a company freight account');
        return;
      }
      // Check both Chart of Accounts (new) and legacy account selections for sales tax
      if (selectedSalesTaxChartAccount.value == null &&
          selectedSalesTaxAccount.value == null) {
        SnackbarUtils.showError(
            'Validation Error', 'Please select a sales tax account');
        return;
      }
      // Check both Chart of Accounts (new) and legacy account selections for freight tax
      if (selectedFreightTaxChartAccount.value == null &&
          selectedFreightTaxAccount.value == null) {
        SnackbarUtils.showError(
            'Validation Error', 'Please select a freight tax account');
        return;
      }
      // Check both Chart of Accounts (new) and legacy account selections for profit
      if (selectedProfitChartAccount.value == null &&
          selectedProfitAccount.value == null) {
        SnackbarUtils.showError(
            'Validation Error', 'Please select a profit account');
        return;
      }

      // Validate tax authority selection for 15% tax distribution
      if (selectedTaxAuthorities.isEmpty) {
        SnackbarUtils.showError('Tax Authority Required',
            'Please select at least one tax authority for 15% tax distribution');
        return;
      }
      if (selectedTaxAuthorities.length > 2) {
        SnackbarUtils.showError('Tax Authority Limit',
            'You can select a maximum of 2 tax authorities');
        return;
      }
    }

    var voucher = getVoucherFromInput();
    if (voucher != null) {
      _currentVoucherBeingSaved = voucher; // Store for later use

      bool success = false;
      if (editMode == false) {
        // Log account selections before voucher creation
        log('BEFORE VOUCHER CREATION:');
        log('Selected broker account: ${selectedBrokerAccount.value?.name ?? 'None'} (ID: ${selectedBrokerAccount.value?.id ?? 'None'})');
        log('Selected munshiana account: ${selectedMunshianaAccount.value?.name ?? 'None'} (ID: ${selectedMunshianaAccount.value?.id ?? 'None'})');
        log('Broker type: ${voucher.brokerType}');
        log('Broker fees: ${voucher.brokerFees}');
        log('Munshiana fees: ${voucher.munshianaFees}');

        success = await createVoucher(voucher: voucher.toJson());
        // FIXED: Only create account transactions for NEW vouchers
        if (success) {
          // Log account selections after voucher creation
          log('AFTER VOUCHER CREATION:');
          log('Selected broker account: ${selectedBrokerAccount.value?.name ?? 'None'} (ID: ${selectedBrokerAccount.value?.id ?? 'None'})');
          log('Selected munshiana account: ${selectedMunshianaAccount.value?.name ?? 'None'} (ID: ${selectedMunshianaAccount.value?.id ?? 'None'})');

          await _createAccountTransactions(voucher);
        }
      } else {
        success = await updateVoucher(voucher: voucher.toJson());
        // FIXED: For updates, create account transactions for NEW local payments AND broker/munshiana fees
        if (success) {
          await _createAccountTransactionsForNewPayments(voucher);
        }
      }
    }
  }

  VoucherModel? getVoucherFromInput() {
    if (!addVoucherFormStateKey.currentState!.validate()) {
      // If the form is not valid, return null
      showErrorSnackbar('All Fields Must Be Valid Filled: Check Red Fields');
      return null;
    }

    // CRITICAL FIX: Combine existing and local payments for saving
    final allPayments = <PaymentTransactionModel>[];

    // Add existing payments (in edit mode)
    allPayments.addAll(existingPaymentTransactions);

    // Add new local payments
    allPayments.addAll(localPaymentTransactions);

    return VoucherModel(
      voucherStatus: voucherStatusSelected.value,
      driverName: driverNameController.text,
      invoiceTasNumberList: stringTagController.getTags,
      invoiceBiltyNumberList: invoiceBiltyNumberList.toList(),
      weightInTons: double.tryParse(weightInTonsController.text)?.round() ??
          0, // Fixed: Parse as double then round to int
      voucherNumber: voucherNumberController.text,
      departureDate: departureDateController.text,
      productName: productNameController.text,
      totalNumberOfBags: int.tryParse(totalNumberOfBagsController.text) ?? 0,
      brokerName: _getBrokerName(),
      driverPhoneNumber: driverPhoneNumberController.text,
      truckNumber: truckNumberController.text,
      conveyNoteNumber: conveyNoteNumberController.text,
      totalFreight: double.tryParse(totalTruckFreight.text) ?? 0.0,
      companyFreight: double.tryParse(companyFreightController.text) ?? 0.0,
      settledFreight: settledFreight.value,
      paymentTransactions: allPayments.map((p) => p.toMap()).toList(),
      brokerType: brokerType.value,
      selectedBroker: selectedBroker.value,
      brokerFees: double.tryParse(brokerFeesController.text) ?? 0.0,
      munshianaFees: double.tryParse(munshianaFeesController.text) ?? 0.0,
      brokerAccount: selectedBrokerAccount.value?.id ?? '',
      munshianaAccount: selectedMunshianaAccount.value?.id ?? '',
      // New fields
      brokerList: selectedBrokersList.map((b) => b.id).toList(),
      calculatedProfit: calculatedProfit.value,
      calculatedTax: calculatedSalesTax.value,
      calculatedFreightTax: calculatedFreightTax.value,
      // Chart of Accounts fields
      brokerAccountId: selectedBrokerChartAccount.value?.id ??
          selectedBrokerAccount.value?.id,
      munshianaAccountId: selectedMunshianaChartAccount.value?.id ??
          selectedMunshianaAccount.value?.id,
      salesTaxAccountId: selectedSalesTaxChartAccount.value?.id ??
          selectedSalesTaxAccount.value?.id,
      freightTaxAccountId: selectedFreightTaxChartAccount.value?.id ??
          selectedFreightTaxAccount.value?.id,
      profitAccountId: selectedProfitChartAccount.value?.id ??
          selectedProfitAccount.value?.id,
      truckFreightAccountId: selectedTruckFreightChartAccount.value?.id,
      // Legacy account name fields
      taxAccountName: selectedSalesTaxChartAccount.value?.displayName ??
          selectedSalesTaxAccount.value?.name,
      freightTaxAccountName:
          selectedFreightTaxChartAccount.value?.displayName ??
              selectedFreightTaxAccount.value?.name,
      profitAccountName: selectedProfitChartAccount.value?.displayName ??
          selectedProfitAccount.value?.name,
      companyFreightAccountId: selectedCompanyFreightChartAccount.value?.id ??
          selectedCompanyFreightAccount.value?.id,
      companyFreightAccountName:
          selectedCompanyFreightChartAccount.value?.displayName ??
              selectedCompanyFreightAccount.value?.name,
      brokerCompanyId: selectedBrokerCompany.value?.uid,
      brokerCompanyName: selectedBrokerCompany.value?.companyName,
      selectedTaxAuthorities: selectedTaxAuthorities.toList(),
    );
  }

  /// Create voucher and handle success or error responses.
  Future<bool> updateVoucher({
    required Map<String, dynamic> voucher,
  }) async {
    // Show loading dialog
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      // Call the use case
      final result = await updateVoucherUseCase.call(voucher: voucher);

      // Close loading dialog
      Get.back();

      // Handle result
      return result.fold(
        (failure) {
          showErrorDialog(failure);
          return false;
        },
        (success) {
          _showSuccessSnackbar(success);
          return true;
        },
      );
    } catch (e) {
      // Close loading dialog
      Get.back();

      // Show unexpected error dialog
      Get.defaultDialog(
        title: "Unexpected Error",
        middleText: "Something went wrong. Please try again later.",
        textConfirm: "OK",
        confirmTextColor: Colors.white,
        onConfirm: () => Get.back(),
      );
      return false;
    }
  }

  /// Updates an invoice in Firestore.

  Future<bool> createVoucher({
    required Map<String, dynamic> voucher,
  }) async {
    // Show loading dialog
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      // Call the use case
      final result = await createVoucherUseCase.call(
        voucher: voucher,
      );

      // Close loading dialog
      Get.back();

      // Handle result
      return result.fold(
        (failure) {
          showErrorDialog(failure);
          return false;
        },
        (success) {
          // Manually add the voucher to the company's list for immediate display
          if (_currentVoucherBeingSaved != null) {
            log('AddVoucherController: Manually adding voucher ${_currentVoucherBeingSaved!.voucherNumber}');
            companyController.company.addVoucher(_currentVoucherBeingSaved!);
          }
          _showSuccessSnackbar(success);
          return true;
        },
      );
    } catch (e) {
      // Close loading dialog
      Get.back();

      // Show unexpected error
      Get.defaultDialog(
        title: "Unexpected Error",
        middleText: "Something went wrong. Please try again later.",
        textConfirm: "OK",
        confirmTextColor: Colors.white,
        onConfirm: () => Get.back(),
      );
      return false;
    }
  }

  /// Show an error popup with details.

  void showErrorSnackbar(String errorMessage) {
    if (errorMessage.isNotEmpty) {
      (failure) => SnackbarUtils.showError(
            AppStrings.error, // Title of the snackbar
            errorMessage, // Error message content
          );
    }
  }

  /// Show a success snackbar.
  void _showSuccessSnackbar(SuccessObj success) {
    Get.back(); // Close any active dialogs
    displaySuccessSnackbar(success);

    // Force refresh both company controller and voucher list controller
    _refreshVoucherData();
  }

  void _refreshVoucherData() {
    // Force refresh the company controller
    companyController.forceRefresh();

    // Immediately refresh the voucher list controller
    try {
      final voucherListController = Get.find<VoucherListController>();
      voucherListController.refreshVouchers();
    } catch (e) {
      // Voucher list controller might not be initialized, ignore
    }

    // Add a small delay to ensure Firestore has processed the change, then refresh again
    Future.delayed(const Duration(milliseconds: 500), () {
      try {
        // Force update the company controller
        companyController.forceRefresh();

        // Force refresh the voucher list controller again
        final voucherListController = Get.find<VoucherListController>();
        voucherListController
            .setSearchQuery(voucherListController.searchQuery.value);
      } catch (e) {
        // Controllers might not be initialized, ignore
      }
    });
  }

  void togglePaymentType(String type) {
    if (selectedPaymentTypes.contains(type)) {
      selectedPaymentTypes.remove(type);
    } else {
      selectedPaymentTypes.add(type);
    }
  }

  void setBrokerType(String type) {
    // Only reset fields if the broker type is actually changing
    if (brokerType.value != type) {
      brokerType.value = type;

      // Reset related fields based on broker type, but preserve values if editing existing voucher
      if (type == BrokerType.own.value) {
        // For Own broker, clear outsource-related fields only if not editing
        if (currentVoucher == null) {
          selectedPayee.value = null;
          selectedBrokerAccount.value = null;
          brokerNameController.clear();
          brokerFeesController.clear();
          brokerAccount.value = '';
          selectedBroker.value = '';
        }
      } else if (type == BrokerType.outsource.value) {
        // For Outsource broker, clear own broker selection only if not editing
        if (currentVoucher == null) {
          selectedBroker.value = '';
          brokerNameController.clear();
        }
      }

      // Munshiana account should not be cleared as it's independent of broker type
      // munshianaController and selectedMunshianaAccount should remain
    } else {
      brokerType.value = type;
    }
    update();
  }

  void setBrokerName(String name) {
    brokerNameController.text = name;
    selectedBroker.value = name; // Also set the selected broker for "Own" type
    update();
  }

  /// Get the correct broker name based on broker type
  String _getBrokerName() {
    if (brokerType.value == BrokerType.own.value) {
      // For own broker, use the selected broker name
      return selectedBroker.value.isNotEmpty
          ? selectedBroker.value
          : brokerNameController.text;
    } else if (brokerType.value == BrokerType.outsource.value) {
      // For outsource broker, use the selected payee name
      return selectedPayee.value?.name ?? brokerNameController.text;
    }
    // Fallback to controller text
    return brokerNameController.text;
  }

  void removeTasNumber(String tas) {
    invoiceTasNumberList.remove(tas);
  }

  // Calculate profit/loss
  double calculateProfit() {
    double companyFreight = double.tryParse(companyFreightController.text) ?? 0;
    double totalFreightAmount = double.tryParse(totalTruckFreight.text) ?? 0;

    // Calculate 4.6% sales tax on company freight
    double salesTax = companyFreight * 0.046;

    // Calculate profit: company freight - sales tax - truck freight (excluding company freight tax)
    return companyFreight - salesTax - totalFreightAmount;
  }

  void toggleDieselCard() {
    dieselCardSelected.value = !dieselCardSelected.value;
    update();
  }

  void toggleCheque() {
    chequeSelected.value = !chequeSelected.value;
    update();
  }

  Rx<double> calulateProfitLose = 0.0.obs;
  String calculateProfitLoss() {
    double companyFreight = double.tryParse(companyFreightController.text) ?? 0;
    double totalFreightAmount = double.tryParse(totalTruckFreight.text) ?? 0;

    // Calculate 4.6% sales tax on company freight
    double salesTax = companyFreight * 0.046;

    // Calculate profit: company freight - sales tax - truck freight (excluding company freight tax)
    calulateProfitLose.value = companyFreight - salesTax - totalFreightAmount;
    return (calulateProfitLose.value).toString();
  }

  // Get profit/loss breakdown formula text
  String getProfitLossBreakdown() {
    double companyFreight = double.tryParse(companyFreightController.text) ?? 0;
    double totalFreightAmount = double.tryParse(totalTruckFreight.text) ?? 0;
    double salesTax = companyFreight * 0.046;
    double companyFreightTax = companyFreight > 0 ? companyFreight * 0.15 : 0.0;
    double profitLoss = companyFreight - salesTax - totalFreightAmount;

    return "Company Freight (${companyFreight.toStringAsFixed(0)}) - Sales Tax 4.6% (${salesTax.toStringAsFixed(0)}) - Truck Freight (${totalFreightAmount.toStringAsFixed(0)}) = ${profitLoss.toStringAsFixed(0)} | Company Freight Tax 15% (${companyFreightTax.toStringAsFixed(0)}) - For Information Only";
  }

  String? validateBrokerFees(String? value) {
    if (value == null || value.isEmpty) {
      return 'Broker fees is required.';
    }
    if (double.tryParse(value) == null) {
      return 'Broker fees must be a valid number.';
    }
    return null;
  }

  void setBrokerAccount(String accountName) {
    brokerAccount.value = accountName;
    update();
  }

  void setMunshianaAccount(String accountName) {
    munshianaAccount.value = accountName;
    update();
  }

  void ensureTagControllerInitialized() {
    if (tagsInitialized.value) return;

    // Safety check - if somehow the controller is in use, dispose it first
    try {
      stringTagController.clearTags();
      stringTagController.dispose();
    } catch (e) {
      // Ignore errors if it wasn't initialized
    }

    // Create a new controller
    stringTagController = StringTagController();

    // Add tags from invoiceTasNumberList
    if (invoiceTasNumberList.isNotEmpty) {
      // Use a separate list to avoid concurrent modification issues
      final tagsList = List<String>.from(invoiceTasNumberList);
      for (var tag in tagsList) {
        stringTagController.onTagSubmitted(tag);
      }
    }

    tagsInitialized.value = true;
    update();
  }

  // Method to remove a tag from both the controller and the list
  void removeTag(String tag) {
    // First remove from the list to ensure it's not re-added
    invoiceTasNumberList.removeWhere((t) => t == tag);

    try {
      // Then try to remove from controller
      stringTagController.removeTag(tag);
    } catch (e) {
      // Ignore errors if already removed or doesn't exist
    }

    // Update product details to reflect the removed tag
    updateProductDetails();

    // Force an immediate update
    update();
  }

  // Payment transaction management methods

  // Get all payments (existing + local) for display
  List<PaymentTransactionModel> get allPaymentTransactions {
    final allPayments = <PaymentTransactionModel>[];
    allPayments.addAll(existingPaymentTransactions);
    allPayments.addAll(localPaymentTransactions);
    return allPayments;
  }

  // Add a new LOCAL payment transaction (not saved to DB)
  void addLocalPaymentTransaction(PaymentTransactionModel transaction) {
    localPaymentTransactions.add(transaction);
    updateSettledAmount();
    update();
  }

  // Edit an existing LOCAL payment transaction
  void editLocalPaymentTransaction(PaymentTransactionModel transaction) {
    final index =
        localPaymentTransactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      localPaymentTransactions[index] = transaction;
      updateSettledAmount();
      update();
    }
  }

  // Delete a LOCAL payment transaction
  void deleteLocalPaymentTransaction(PaymentTransactionModel transaction) {
    // Check if it's an existing transaction (cannot be deleted in edit mode)
    if (existingPaymentTransactions.any((t) => t.id == transaction.id)) {
      SnackbarUtils.showError(
        'Cannot Delete',
        'Existing payments cannot be deleted when editing a voucher',
      );
      return;
    }

    // Remove from local transactions
    localPaymentTransactions.removeWhere((t) => t.id == transaction.id);
    updateSettledAmount();
    update();
  }

  // Calculate total settled amount from ALL transactions
  void updateSettledAmount() {
    double total = 0.0;

    // Add existing payments
    for (var transaction in existingPaymentTransactions) {
      total += transaction.amount;
    }

    // Add local payments
    for (var transaction in localPaymentTransactions) {
      total += transaction.amount;
    }

    settledFreight.value = total;

    // Trigger form validation to check total freight against settled amount
    Future.delayed(Duration.zero, () {
      addVoucherFormStateKey.currentState?.validate();
    });

    update();
  }

  // Check if a payment transaction can be edited
  bool canEditTransaction(PaymentTransactionModel transaction) {
    // Can always edit local transactions
    if (localPaymentTransactions.any((t) => t.id == transaction.id)) {
      return true;
    }

    // Cannot edit existing transactions
    return false;
  }

  // Check if a payment transaction can be deleted
  bool canDeleteTransaction(PaymentTransactionModel transaction) {
    // Can only delete local transactions
    return localPaymentTransactions.any((t) => t.id == transaction.id);
  }

  // Show payment transaction dialog - UPDATED to use local transactions
  void showPaymentTransactionDialog(BuildContext context,
      {PaymentTransactionModel? transaction}) {
    // Check if trying to edit an existing transaction
    if (transaction != null &&
        existingPaymentTransactions.any((t) => t.id == transaction.id)) {
      SnackbarUtils.showError(
        'Cannot Edit',
        'Existing payments cannot be edited. You can only add new payments.',
      );
      return;
    }

    // Get up-to-date fuel cards if available
    final currentFuelCards = Get.isRegistered<FuelCardController>()
        ? fuelCardController.fuelCards
        : fuelCards;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return PaymentTransactionDialog(
          voucherId:
              currentVoucher?.voucherNumber ?? voucherNumberController.text,
          totalFreight: double.tryParse(totalTruckFreight.text) ?? 0.0,
          settledFreight: settledFreight.value,
          accounts: accounts,
          fuelCards: currentFuelCards,
          transaction: transaction,
          // REMOVED CASH OPTION FROM DIALOG
          allowCashPayments: false, // NEW: Disable cash payments
          // Use voucher departure date as default transaction date
          defaultTransactionDate:
              parseDepartureDate(departureDateController.text),
          onSave: (updatedTransaction) async {
            // Handle fuel card capacity updates if needed
            if (updatedTransaction.method == PaymentMethod.fuelCard &&
                updatedTransaction.fuelCardId != null &&
                updatedTransaction.fuelLiters != null) {
              double previousLiters = 0;
              if (transaction != null &&
                  transaction.method == PaymentMethod.fuelCard &&
                  transaction.fuelLiters != null) {
                previousLiters = transaction.fuelLiters!;
              }

              final litersToReduce =
                  updatedTransaction.fuelLiters! - previousLiters;

              if (litersToReduce > 0) {
                final success = await updateFuelCardCapacity(
                  updatedTransaction.fuelCardId!,
                  litersToReduce,
                );

                if (!success) {
                  SnackbarUtils.showError(
                    'Warning',
                    'Transaction saved but fuel card capacity could not be updated',
                  );
                }
              }
            }

            // CRITICAL FIX: Use local transaction methods
            if (transaction != null) {
              editLocalPaymentTransaction(updatedTransaction);
            } else {
              addLocalPaymentTransaction(updatedTransaction);
            }
          },
        );
      },
    );
  }

  // Load fuel cards (used as fallback if FuelCardController is not available)
  Future<void> loadFuelCards() async {
    try {
      // First try to use the FuelCardController if registered
      if (Get.isRegistered<FuelCardController>()) {
        fuelCardController = Get.find<FuelCardController>();
        try {
          // Try to load cards from the controller
          await fuelCardController.loadFuelCards();
          fuelCards.value = fuelCardController.fuelCards;
          update();
          return;
        } catch (e) {
          log('Error loading fuel cards from controller: $e');
          // Fall back to dummy data if controller fails
        }
      }

      // If controller isn't available or failed, use dummy data as fallback
      fuelCards.value = [
        FuelCardModel(
          id: '2',
          cardNumber: 'FC-002',
          companyName: 'Ahad',
          uid: 'comp-002',
          fuelStationName: 'Shell',
          fuelStationId: 'station-002',
          totalCapacity: 2000,
          remainingCapacity: 1200,
          currentRate: 92.3,
          createdAt: DateTime.now(),
        ),
      ];
      update();
    } catch (e) {
      log('Error loading fuel cards: $e');
      // In case of complete failure, initialize with empty list to avoid null issues
      fuelCards.value = [];
      update();
    }
  }

  // New method to get available fuel stations for dropdown
  List<String> getAvailableFuelStations() {
    if (fuelCards.isEmpty) {
      return ['No Fuel Stations Available'];
    }

    // Extract unique station names
    final stations =
        fuelCards.map((card) => card.fuelStationName).toSet().toList();

    return stations.isEmpty ? ['No Fuel Stations Available'] : stations;
  }

  // New method to get fuel cards filtered by station
  List<FuelCardModel> getFuelCardsByStation(String stationName) {
    if (fuelCards.isEmpty) {
      return [];
    }

    return fuelCards
        .where((card) => card.fuelStationName == stationName)
        .toList();
  }

  // New method to find a specific fuel card by ID
  FuelCardModel? getFuelCardById(String cardId) {
    if (fuelCards.isEmpty) {
      return null;
    }

    try {
      return fuelCards.firstWhere((card) => card.id == cardId);
    } catch (e) {
      log('Fuel card not found: $cardId');
      return null;
    }
  }

  // Update fuel card after usage with better error handling
  Future<bool> updateFuelCardCapacity(String cardId, double usedLiters) async {
    if (Get.isRegistered<FuelCardController>()) {
      try {
        // Use the fuel card controller if available
        return await fuelCardController.updateFuelCardCapacity(
            cardId, usedLiters);
      } catch (e) {
        log('Error updating fuel card capacity via controller: $e');
        // Fall back to direct update if controller fails
      }
    }

    try {
      // Fallback to direct update
      final index = fuelCards.indexWhere((card) => card.id == cardId);
      if (index == -1) {
        log('Fuel card not found: $cardId');
        return false;
      }

      final card = fuelCards[index];
      if (card.remainingCapacity < usedLiters) {
        log('Not enough fuel available on card $cardId: ${card.remainingCapacity}L remaining, trying to use ${usedLiters}L');
        SnackbarUtils.showError(
          'Insufficient Fuel',
          'The selected fuel card does not have enough fuel (${card.remainingCapacity.toStringAsFixed(2)}L remaining)',
        );
        return false;
      }

      final updatedCard = card.copyWith(
        remainingCapacity: card.remainingCapacity - usedLiters,
        updatedAt: DateTime.now(),
      );

      fuelCards[index] = updatedCard;
      update();

      // If using direct update but controller exists, try to sync changes
      if (Get.isRegistered<FuelCardController>()) {
        try {
          fuelCardController.updateFuelCardInList(updatedCard);
        } catch (e) {
          log('Error syncing fuel card update with controller: $e');
        }
      }

      return true;
    } catch (e) {
      log('Error updating fuel card capacity: $e');
      SnackbarUtils.showError(
        'Update Failed',
        'Failed to update fuel card: $e',
      );
      return false;
    }
  }

  // Add a fuel card payment with improved error handling
  Future<bool> addFuelCardPayment(
      String stationName, String cardId, double liters) async {
    try {
      final card = getFuelCardById(cardId);
      if (card == null) {
        SnackbarUtils.showError(
          'Invalid Card',
          'The selected fuel card was not found',
        );
        return false;
      }

      // Verify fuel amount is available
      if (card.remainingCapacity < liters) {
        SnackbarUtils.showError(
          'Insufficient Fuel',
          'The selected fuel card does not have enough fuel (${card.remainingCapacity.toStringAsFixed(2)}L remaining)',
        );
        return false;
      }

      // Calculate amount based on fuel and rate
      final amount = liters * card.currentRate;

      // Ensure we don't exceed the freight amount
      final totalFreightAmount = double.tryParse(totalTruckFreight.text) ?? 0.0;
      if (amount > totalFreightAmount) {
        SnackbarUtils.showError(
          'Excess Payment',
          'Fuel cost (${amount.toStringAsFixed(2)}) cannot exceed total freight (${totalFreightAmount.toStringAsFixed(2)})',
        );
        return false;
      }

      // Get the latest fuel rate for this company to ensure we use current pricing
      double currentRate = card.currentRate;
      if (Get.isRegistered<FuelCardController>()) {
        try {
          final latestRate =
              await fuelCardController.getLatestFuelRate(card.companyName);
          if (latestRate > 0) {
            currentRate = latestRate;
            log('Using latest fuel rate: $currentRate for ${card.companyName}');
          }
        } catch (e) {
          log('Could not get latest fuel rate, using card rate: ${card.currentRate}');
        }
      }

      // Recalculate amount with latest rate
      final updatedAmount = liters * currentRate;

      // Create the payment transaction
      final transaction = PaymentTransactionModel(
        id: const Uuid().v4(),
        voucherId: voucherNumberController.text,
        method: PaymentMethod.fuelCard,
        status: PaymentStatus.paid,
        amount: updatedAmount,
        pendingAmount:
            totalFreightAmount - settledFreight.value - updatedAmount,
        transactionDate: parseDepartureDate(departureDateController.text),
        notes:
            'Fuel payment: ${liters.toStringAsFixed(2)}L at $stationName (Rate: ${currentRate.toStringAsFixed(2)})',
        fuelCardId: card.id,
        fuelCardNumber: card.cardNumber,
        fuelCompany: card.companyName,
        fuelLiters: liters,
        fuelRate: currentRate,
      );

      // Update fuel card capacity
      final cardUpdated = await updateFuelCardCapacity(cardId, liters);
      if (!cardUpdated) {
        return false;
      }

      // Add the transaction
      addLocalPaymentTransaction(transaction);

      return true;
    } catch (e) {
      log('Error creating fuel card payment: $e');
      SnackbarUtils.showError(
        'Payment Failed',
        'Failed to create fuel card payment: $e',
      );
      return false;
    }
  }

  // Fill form with dummy data for testing
  void fillDummyDetails() {
    // Basic voucher information
    voucherNumberController.text =
        DateTime.now().millisecondsSinceEpoch.toString().substring(0, 8);
    voucherStatusSelected.value = VoucherStatus.pending.value;
    departureDateController.text = formatDate(DateTime.now());

    // Add sample convey note numbers
    invoiceTasNumberList.clear();
    invoiceBiltyNumberList.clear();
    stringTagController.clearTags();

    // Driver & transport details
    driverNameController.text = 'John Smith';
    driverPhoneNumberController.text = '03123456789';
    truckNumberController.text = 'ABC1234';

    // Broker information - TEST OUTSOURCE TO SEE FEES LOGIC
    brokerType.value = BrokerType.outsource.value;
    brokerFeesController.text = '5000';
    munshianaFeesController.text = '2000';

    // Payment details
    totalTruckFreight.text = '50000';
    companyFreightController.text = '45000';

    // REMOVED CASH PAYMENT FROM DUMMY DATA
    dieselCardSelected.value = true;
    chequeSelected.value = true;

    // Clear previous local payments and add dummy ones
    localPaymentTransactions.clear();

    // Add a check payment
    localPaymentTransactions.add(
      PaymentTransactionModel(
        id: const Uuid().v4(),
        voucherId: voucherNumberController.text,
        method: PaymentMethod.check,
        status: PaymentStatus.partial,
        amount: 20000,
        pendingAmount: 30000,
        transactionDate: parseDepartureDate(departureDateController.text),
        notes: 'Partial payment by check',
        checkNumber: 'CHK-8765',
        bankName: 'National Bank',
      ),
    );

    // Add a fuel card payment if available
    if (fuelCards.isNotEmpty) {
      final fuelCard = fuelCards.first;
      final fuelLiters = 100.0;
      final amount = fuelLiters * fuelCard.currentRate;

      localPaymentTransactions.add(
        PaymentTransactionModel(
          id: const Uuid().v4(),
          voucherId: voucherNumberController.text,
          method: PaymentMethod.fuelCard,
          status: PaymentStatus.paid,
          amount: amount,
          pendingAmount: 20000,
          transactionDate: parseDepartureDate(departureDateController.text),
          notes: 'Fuel card payment',
          fuelCardId: fuelCard.id,
          fuelCardNumber: fuelCard.cardNumber,
          fuelCompany: fuelCard.companyName,
          fuelLiters: fuelLiters,
          fuelRate: fuelCard.currentRate,
        ),
      );
    }

    updateSettledAmount();
    update();
  }

  set setStatus(String value) {
    voucherStatusSelected.value = value;
  }

  void setVoucherStatus(String value) {
    voucherStatusSelected.value = value;
    update();
  }

  // Create account transactions for new payments only (used in edit mode)
  Future<void> _createAccountTransactionsForNewPayments(
      VoucherModel voucher) async {
    try {
      log('Creating account transactions for NEW payments only in voucher: ${voucher.voucherNumber}');
      log('Local payment transactions count: ${localPaymentTransactions.length}');

      // Handle broker fees based on broker type (only if not already created)
      if (voucher.brokerFees > 0) {
        await _handleBrokerFeesForEdit(voucher);
      }

      // Handle munshiana fees - Create as a DEPOSIT (money coming IN to munshiana account)
      if (voucher.munshianaFees > 0) {
        await _handleMunshianaFeesForEdit(voucher);
      }

      // Handle new accounting entries for Company Freight, Tax, and Profit (for edit mode)
      await _createFinancialAccountingEntries(voucher);

      // Handle only NEW local payment transactions as expenses (money going out)
      for (final paymentTransaction in localPaymentTransactions) {
        log('Processing NEW payment: ${paymentTransaction.method} amount: ${paymentTransaction.amount} accountId: ${paymentTransaction.accountId}');

        // Create fuel card usage record if this is a fuel card payment
        if (paymentTransaction.method == PaymentMethod.fuelCard &&
            paymentTransaction.fuelCardId != null &&
            paymentTransaction.fuelLiters != null) {
          await _createFuelCardUsageRecord(paymentTransaction, voucher);
        }

        // Create check usage record if this is a check payment
        if (paymentTransaction.method == PaymentMethod.check &&
            paymentTransaction.checkNumber != null &&
            paymentTransaction.checkNumber!.isNotEmpty) {
          await _createCheckUsageRecord(paymentTransaction, voucher);
        }

        if (paymentTransaction.accountId == null ||
            paymentTransaction.accountId!.isEmpty) {
          log('Skipping payment transaction - no account ID');
          continue;
        }

        // Check if this is a cross-company check payment
        bool isCrossCompanyCheck =
            paymentTransaction.method == PaymentMethod.check &&
                paymentTransaction.notes != null &&
                paymentTransaction.notes!.contains('Cross-company cheque:');

        AccountModel? account;

        if (isCrossCompanyCheck) {
          // For cross-company checks, we don't need to validate the account in current user's accounts
          // The account belongs to another company, so we'll create a placeholder for processing
          log('Processing cross-company check payment - skipping current user account validation');
          account = AccountModel(
            id: paymentTransaction.accountId!,
            name: paymentTransaction.accountName ?? 'External Company Account',
            initialBalance: 0,
            accountNumber: '',
            branchCode: '',
            branchAddress: '',
            availableBalance: 0,
            createdAt: DateTime.now(),
            uid: '', // External company's UID
          );
        } else {
          // For regular payments, find the account in current user's accounts
          account = accounts.firstWhereOrNull(
            (a) => a.id == paymentTransaction.accountId,
          );

          if (account == null) {
            log('Account not found for payment transaction. AccountId: ${paymentTransaction.accountId}');
            continue; // Skip this transaction
          }
        }

        if (account.id.isNotEmpty) {
          log('Creating expense for NEW payment transaction: ${paymentTransaction.amount} from account: ${account.name}');

          // Skip expense creation for cross-company checks - these will be handled after loan approval
          if (isCrossCompanyCheck) {
            log('Skipping expense creation for cross-company check - will be handled after loan approval');
            continue;
          }

          // FIXED: Create only ONE accounting entry per payment instead of both expense and account transaction
          // Use the expense repository to create a proper expense for payments (this handles the accounting automatically)
          final expenseRepository = ExpenseRepositoryImpl(
            ExpenseFirebaseService(),
            AccountFirebaseService(),
          );

          // Prepare detailed payment method information for both title and notes
          String paymentMethodDetails = '';
          String titlePaymentMethod = '';

          if (paymentTransaction.method == PaymentMethod.check) {
            paymentMethodDetails =
                'Check #${paymentTransaction.checkNumber ?? 'N/A'}';
            titlePaymentMethod =
                'Check (#${paymentTransaction.checkNumber ?? 'N/A'})';
            if (paymentTransaction.bankName != null &&
                paymentTransaction.bankName!.isNotEmpty) {
              paymentMethodDetails += ' (${paymentTransaction.bankName})';
            }
          } else if (paymentTransaction.method ==
              PaymentMethod.accountTransfer) {
            paymentMethodDetails = 'Account Transfer';
            titlePaymentMethod = 'Account Transfer';
          } else if (paymentTransaction.method == PaymentMethod.fuelCard) {
            paymentMethodDetails =
                'Fuel Card #${paymentTransaction.fuelCardNumber ?? 'N/A'}';
            titlePaymentMethod =
                'Fuel Card (#${paymentTransaction.fuelCardNumber ?? 'N/A'})';
            if (paymentTransaction.fuelCompany != null &&
                paymentTransaction.fuelCompany!.isNotEmpty) {
              paymentMethodDetails += ' (${paymentTransaction.fuelCompany})';
            }
          } else if (paymentTransaction.method == PaymentMethod.cash) {
            paymentMethodDetails = 'Cash Payment';
            titlePaymentMethod = 'Cash Payment';
          }

          final paymentExpense = ExpenseModel(
            id: '', // Will be generated by Firebase
            title:
                'Voucher Payment V-${voucher.voucherNumber} - $titlePaymentMethod',
            accountId: account.id,
            accountName: account.name,
            amount: paymentTransaction.amount,
            createdAt: paymentTransaction.transactionDate,
            categoryId: 'voucher_payment',
            categoryName: 'Voucher Payments',
            payeeId: '',
            payeeName: voucher.driverName.isNotEmpty
                ? voucher.driverName
                : 'Driver Payment',
            referenceNumber: 'V-${voucher.voucherNumber}',
            notes:
                'V-${voucher.voucherNumber} - $paymentMethodDetails - ${paymentTransaction.notes ?? "Voucher payment"}',
          );

          final expenseResult =
              await expenseRepository.createExpense(paymentExpense);
          expenseResult.fold(
            (failure) => {
              log('Failed to create payment expense: ${failure.message}'),
              SnackbarUtils.showError('Payment Error',
                  'Failed to create payment expense: ${failure.message}'),
            },
            (success) => {
              log('Successfully created consolidated payment expense with method details'),
            },
          );
        } else {
          log('ERROR: Account not found for payment transaction. AccountId: ${paymentTransaction.accountId}');
        }
      }

      // Reload accounts to get updated balances
      await loadAccounts();

      // Force refresh account controller to update UI immediately
      try {
        if (Get.isRegistered<AccountController>()) {
          final accountController = Get.find<AccountController>();
          await accountController.forceRefresh();
          log('Forced refresh of account controller');
        }
      } catch (e) {
        log('Could not refresh account controller: $e');
      }

      log('Completed creating account transactions for NEW payments in voucher: ${voucher.voucherNumber}');
    } catch (e) {
      log('Error creating account transactions for new payments: $e');
      SnackbarUtils.showError(
          'Transaction Error', 'Error creating account transactions: $e');
    }
  }

  // Create account transactions based on voucher data
  Future<void> _createAccountTransactions(VoucherModel voucher) async {
    try {
      log('Creating account transactions for voucher: ${voucher.voucherNumber}');
      log('Selected broker Chart account: ${selectedBrokerChartAccount.value?.displayName ?? 'None'}');
      log('Selected munshiana Chart account: ${selectedMunshianaChartAccount.value?.displayName ?? 'None'}');
      log('Selected broker legacy account: ${selectedBrokerAccount.value?.name ?? 'None'}');
      log('Selected munshiana legacy account: ${selectedMunshianaAccount.value?.name ?? 'None'}');
      log('Broker type: ${voucher.brokerType}');
      log('Broker fees: ${voucher.brokerFees}');
      log('Munshiana fees: ${voucher.munshianaFees}');
      log('Payment transactions count: ${voucher.paymentTransactions.length}');

      // Check if we should use Chart of Accounts or legacy system
      final useChartOfAccounts = selectedBrokerChartAccount.value != null ||
          selectedMunshianaChartAccount.value != null ||
          selectedSalesTaxChartAccount.value != null ||
          selectedFreightTaxChartAccount.value != null ||
          selectedProfitChartAccount.value != null ||
          selectedTruckFreightChartAccount.value != null ||
          selectedCompanyFreightChartAccount.value != null;

      if (useChartOfAccounts) {
        log('Using Chart of Accounts system - journal entries will be created automatically by the hook service');
        // NOTE: Journal entries are now created automatically by VoucherAccountingHookService
        // through the AutomaticJournalEntryService, so we don't need to create them here
        return;
      }

      log('Using legacy account system for transaction creation');

      // Handle broker fees based on broker type
      if (voucher.brokerFees > 0) {
        final brokerAccount = selectedBrokerAccount.value;
        log('Checking broker account for fees: ${brokerAccount?.name ?? 'null'} (ID: ${brokerAccount?.id ?? 'null'})');
        if (brokerAccount != null && brokerAccount.id.isNotEmpty) {
          if (voucher.brokerType == BrokerType.own.value) {
            // Own broker fees → DEPOSIT (money coming IN to broker account)
            log('Creating own broker fees deposit: ${voucher.brokerFees} to account: ${brokerAccount.name}');

            final depositRepository =
                DepositRepositoryImpl(DepositFirebaseService());

            final brokerDeposit = DepositModel(
              id: '', // Will be generated by Firebase
              accountId: brokerAccount.id,
              accountName: brokerAccount.name,
              amount: voucher.brokerFees,
              createdAt: parseDepartureDate(voucher.departureDate),
              categoryId: 'broker_fees',
              categoryName: 'Broker Fees',
              payerId: '',
              payerName: voucher.brokerName.isNotEmpty
                  ? voucher.brokerName
                  : 'Own Broker',
              referenceNumber: 'V-${voucher.voucherNumber}',
              notes: 'Own broker fees for voucher V-${voucher.voucherNumber}',
            );

            final depositResult =
                await depositRepository.createDeposit(brokerDeposit);
            depositResult.fold(
              (failure) => {
                log('Failed to create own broker fees deposit: ${failure.message}'),
                SnackbarUtils.showError('Broker Fees Error',
                    'Failed to create own broker fees deposit: ${failure.message}'),
              },
              (success) => {
                log('Successfully created own broker fees deposit'),
                SnackbarUtils.showSuccess(
                    'Success', 'Own broker fees deposit created successfully'),
              },
            );
          } else if (voucher.brokerType == BrokerType.outsource.value) {
            // Outsource broker fees → EXPENSE (money going OUT from broker account)
            log('Creating outsource broker fees expense: ${voucher.brokerFees} from account: ${brokerAccount.name}');

            final expenseRepository = ExpenseRepositoryImpl(
              ExpenseFirebaseService(),
              AccountFirebaseService(),
            );

            final brokerExpense = ExpenseModel(
              id: '', // Will be generated by Firebase
              title: 'Outsource Broker Fees - V-${voucher.voucherNumber}',
              accountId: brokerAccount.id,
              accountName: brokerAccount.name,
              amount: voucher.brokerFees,
              createdAt: parseDepartureDate(voucher.departureDate),
              categoryId: 'broker_fees',
              categoryName: 'Broker Fees',
              payeeId: '',
              payeeName: voucher.brokerName.isNotEmpty
                  ? voucher.brokerName
                  : 'Outsource Broker',
              referenceNumber: 'V-${voucher.voucherNumber}',
              notes:
                  'Outsource broker fees for voucher V-${voucher.voucherNumber}',
            );

            final expenseResult =
                await expenseRepository.createExpense(brokerExpense);
            expenseResult.fold(
              (failure) => {
                log('Failed to create outsource broker fees expense: ${failure.message}'),
                SnackbarUtils.showError('Broker Fees Error',
                    'Failed to create outsource broker fees expense: ${failure.message}'),
              },
              (success) => {
                log('Successfully created outsource broker fees expense'),
                SnackbarUtils.showSuccess('Success',
                    'Outsource broker fees expense created successfully'),
              },
            );
          }
        } else {
          log('ERROR: Broker account not selected or invalid when trying to create broker fees');
          log('Broker account value: ${selectedBrokerAccount.value}');
          log('Available accounts: ${accounts.map((a) => '${a.name} (${a.id})').join(', ')}');
          SnackbarUtils.showError('Broker Account Error',
              'Please select an account for broker fees.');
        }
      }

      // Handle munshiana fees - Create as a DEPOSIT (money coming IN to munshiana account)
      if (voucher.munshianaFees > 0) {
        final munshianaAccount = selectedMunshianaAccount.value;
        log('Checking munshiana account for fees: ${munshianaAccount?.name ?? 'null'} (ID: ${munshianaAccount?.id ?? 'null'})');
        if (munshianaAccount != null && munshianaAccount.id.isNotEmpty) {
          log('Creating munshiana fees deposit: ${voucher.munshianaFees} to account: ${munshianaAccount.name}');

          final depositRepository =
              DepositRepositoryImpl(DepositFirebaseService());

          final munshianaDeposit = DepositModel(
            id: '', // Will be generated by Firebase
            accountId: munshianaAccount.id,
            accountName: munshianaAccount.name,
            amount: voucher.munshianaFees,
            createdAt: parseDepartureDate(voucher.departureDate),
            categoryId: 'munshiana',
            categoryName: 'Munshiana',
            payerId: '',
            payerName: 'Munshiana Payment',
            referenceNumber: 'V-${voucher.voucherNumber}',
            notes: 'Munshiana payment for voucher V-${voucher.voucherNumber}',
          );

          final depositResult =
              await depositRepository.createDeposit(munshianaDeposit);
          depositResult.fold(
            (failure) => {
              log('Failed to create munshiana deposit: ${failure.message}'),
              SnackbarUtils.showError('Munshiana Error',
                  'Failed to create munshiana deposit: ${failure.message}'),
            },
            (success) => {
              log('Successfully created munshiana deposit'),
              SnackbarUtils.showSuccess(
                  'Success', 'Munshiana deposit created successfully'),
            },
          );
        } else {
          log('ERROR: Munshiana account not selected or invalid when trying to create munshiana deposit');
          log('Munshiana account value: ${selectedMunshianaAccount.value}');
          log('Available accounts: ${accounts.map((a) => '${a.name} (${a.id})').join(', ')}');
          SnackbarUtils.showError('Munshiana Account Error',
              'Please select an account for munshiana fees.');
        }
      }

      // Handle new accounting entries for Company Freight, Tax, and Profit
      await _createFinancialAccountingEntries(voucher);

      // Handle regular payment transactions as expenses (money going out)
      final allPayments = <PaymentTransactionModel>[];
      for (final payment in voucher.paymentTransactions) {
        final paymentTransaction = PaymentTransactionModel.fromMap(payment);
        allPayments.add(paymentTransaction);
      }

      log('Processing ${allPayments.length} payment transactions');

      for (final paymentTransaction in allPayments) {
        log('Processing payment: ${paymentTransaction.method} amount: ${paymentTransaction.amount} accountId: ${paymentTransaction.accountId}');

        // Create fuel card usage record if this is a fuel card payment
        if (paymentTransaction.method == PaymentMethod.fuelCard &&
            paymentTransaction.fuelCardId != null &&
            paymentTransaction.fuelLiters != null) {
          await _createFuelCardUsageRecord(paymentTransaction, voucher);
        }

        // Create check usage record if this is a check payment
        if (paymentTransaction.method == PaymentMethod.check &&
            paymentTransaction.checkNumber != null &&
            paymentTransaction.checkNumber!.isNotEmpty) {
          await _createCheckUsageRecord(paymentTransaction, voucher);
        }

        if (paymentTransaction.accountId == null ||
            paymentTransaction.accountId!.isEmpty) {
          log('Skipping payment transaction - no account ID');
          continue;
        }

        // Check if this is a cross-company check payment
        bool isCrossCompanyCheck =
            paymentTransaction.method == PaymentMethod.check &&
                paymentTransaction.notes != null &&
                paymentTransaction.notes!.contains('Cross-company cheque:');

        AccountModel? account;

        if (isCrossCompanyCheck) {
          // For cross-company checks, we don't need to validate the account in current user's accounts
          // The account belongs to another company, so we'll create a placeholder for processing
          log('Processing cross-company check payment - skipping current user account validation');
          account = AccountModel(
            id: paymentTransaction.accountId!,
            name: paymentTransaction.accountName ?? 'External Company Account',
            initialBalance: 0,
            accountNumber: '',
            branchCode: '',
            branchAddress: '',
            availableBalance: 0,
            createdAt: DateTime.now(),
            uid: '', // External company's UID
          );
        } else {
          // For regular payments, find the account in current user's accounts
          account = accounts.firstWhereOrNull(
            (a) => a.id == paymentTransaction.accountId,
          );

          if (account == null) {
            log('Account not found for payment transaction. AccountId: ${paymentTransaction.accountId}');
            continue; // Skip this transaction
          }
        }

        if (account.id.isNotEmpty) {
          log('Creating consolidated accounting entry for payment transaction: ${paymentTransaction.amount} from account: ${account.name}');

          // Skip expense creation for cross-company checks - these will be handled after loan approval
          if (isCrossCompanyCheck) {
            log('Skipping expense creation for cross-company check in edit mode - will be handled after loan approval');
            continue;
          }

          // FIXED: Create only ONE accounting entry per payment instead of both expense and account transaction
          // Use the expense repository to create a proper expense for payments (this handles the accounting automatically)
          final expenseRepository = ExpenseRepositoryImpl(
            ExpenseFirebaseService(),
            AccountFirebaseService(),
          );

          // Prepare detailed payment method information for both title and notes
          String paymentMethodDetails = '';
          String titlePaymentMethod = '';

          if (paymentTransaction.method == PaymentMethod.check) {
            paymentMethodDetails =
                'Check #${paymentTransaction.checkNumber ?? 'N/A'}';
            titlePaymentMethod =
                'Check (#${paymentTransaction.checkNumber ?? 'N/A'})';
            if (paymentTransaction.bankName != null &&
                paymentTransaction.bankName!.isNotEmpty) {
              paymentMethodDetails += ' (${paymentTransaction.bankName})';
            }
          } else if (paymentTransaction.method ==
              PaymentMethod.accountTransfer) {
            paymentMethodDetails = 'Account Transfer';
            titlePaymentMethod = 'Account Transfer';
          } else if (paymentTransaction.method == PaymentMethod.fuelCard) {
            paymentMethodDetails =
                'Fuel Card #${paymentTransaction.fuelCardNumber ?? 'N/A'}';
            titlePaymentMethod =
                'Fuel Card (#${paymentTransaction.fuelCardNumber ?? 'N/A'})';
            if (paymentTransaction.fuelCompany != null &&
                paymentTransaction.fuelCompany!.isNotEmpty) {
              paymentMethodDetails += ' (${paymentTransaction.fuelCompany})';
            }
          } else if (paymentTransaction.method == PaymentMethod.cash) {
            paymentMethodDetails = 'Cash Payment';
            titlePaymentMethod = 'Cash Payment';
          }

          final paymentExpense = ExpenseModel(
            id: '', // Will be generated by Firebase
            title:
                'Voucher Payment V-${voucher.voucherNumber} - $titlePaymentMethod',
            accountId: account.id,
            accountName: account.name,
            amount: paymentTransaction.amount,
            createdAt: paymentTransaction.transactionDate,
            categoryId: 'voucher_payment',
            categoryName: 'Voucher Payments',
            payeeId: '',
            payeeName: voucher.driverName.isNotEmpty
                ? voucher.driverName
                : 'Driver Payment',
            referenceNumber: 'V-${voucher.voucherNumber}',
            notes:
                'V-${voucher.voucherNumber} - $paymentMethodDetails - ${paymentTransaction.notes ?? "Voucher payment"}',
          );

          final expenseResult =
              await expenseRepository.createExpense(paymentExpense);
          expenseResult.fold(
            (failure) => {
              log('Failed to create payment expense: ${failure.message}'),
              SnackbarUtils.showError('Payment Error',
                  'Failed to create payment expense: ${failure.message}'),
            },
            (success) => {
              log('Successfully created consolidated payment expense with method details'),
            },
          );
        } else {
          log('ERROR: Account not found for payment transaction. AccountId: ${paymentTransaction.accountId}');
          SnackbarUtils.showError(
              'Account Error', 'Account not found for payment transaction');
        }
      }

      // Reload accounts to get updated balances
      await loadAccounts();

      // Force refresh account controller to update UI immediately
      try {
        if (Get.isRegistered<AccountController>()) {
          final accountController = Get.find<AccountController>();
          await accountController.forceRefresh();
          log('Forced refresh of account controller');
        }
      } catch (e) {
        log('Could not refresh account controller: $e');
      }

      // Force refresh deposit and expense controllers to show new records
      try {
        if (Get.isRegistered<DepositController>()) {
          final depositController = Get.find<DepositController>();
          depositController.forceRefresh();
          log('Forced refresh of deposit controller');
        }
      } catch (e) {
        log('Could not refresh deposit controller: $e');
      }

      try {
        // Import and refresh expense controller if available
        final expenseControllerType =
            Get.find<dynamic>(tag: 'ExpenseController');
        if (expenseControllerType != null) {
          // Call forceRefresh if the method exists
          try {
            expenseControllerType.forceRefresh();
            log('Forced refresh of expense controller');
          } catch (e) {
            log('Expense controller does not have forceRefresh method: $e');
          }
        }
      } catch (e) {
        log('Could not find or refresh expense controller: $e');
      }

      log('Completed creating account transactions for voucher: ${voucher.voucherNumber}');
    } catch (e) {
      log('Error creating account transactions: $e');
      SnackbarUtils.showError(
          'Transaction Error', 'Error creating account transactions: $e');
      // Don't rethrow - this is a secondary operation, voucher creation should still succeed
    }
  }

  // NOTE: Chart of Accounts transactions are now handled automatically by
  // VoucherAccountingHookService through AutomaticJournalEntryService
  // This provides proper double-entry bookkeeping and better integration

  // NOTE: Direct journal entry creation has been removed.
  // All journal entries are now handled automatically by VoucherAccountingHookService
  // through AutomaticJournalEntryService for proper double-entry bookkeeping.

  // Create fuel card usage record for tracking
  Future<void> _createFuelCardUsageRecord(
      PaymentTransactionModel paymentTransaction, VoucherModel voucher) async {
    try {
      log('Creating fuel card usage record for voucher: ${voucher.voucherNumber}');

      final fuelCardUsageService = FuelCardUsageFirebaseService();

      final usageRecord = FuelCardUsageModel(
        id: const Uuid().v4(),
        fuelCardId: paymentTransaction.fuelCardId!,
        fuelCardNumber: paymentTransaction.fuelCardNumber ?? '',
        companyName: paymentTransaction.fuelCompany ?? '',
        voucherId: voucher.voucherNumber,
        voucherNumber: voucher.voucherNumber,
        litersUsed: paymentTransaction.fuelLiters!,
        rateAtTime: paymentTransaction.fuelRate ?? 0.0,
        totalAmount: paymentTransaction.amount,
        usageDate: paymentTransaction.transactionDate,
        driverName: voucher.driverName,
        truckNumber: voucher.truckNumber,
        notes: paymentTransaction.notes ??
            'Fuel used for voucher ${voucher.voucherNumber}',
      );

      final result =
          await fuelCardUsageService.createFuelCardUsage(usageRecord);
      result.fold(
        (failure) => {
          log('Failed to create fuel card usage record: ${failure.message}'),
          SnackbarUtils.showError('Fuel Usage Error',
              'Failed to record fuel card usage: ${failure.message}'),
        },
        (success) => {
          log('Successfully created fuel card usage record'),
        },
      );
    } catch (e) {
      log('Error creating fuel card usage record: $e');
      SnackbarUtils.showError(
          'Fuel Usage Error', 'Error recording fuel card usage: $e');
    }
  }

  // Create check usage record for tracking
  Future<void> _createCheckUsageRecord(
      PaymentTransactionModel paymentTransaction, VoucherModel voucher) async {
    try {
      log('Creating check usage record for voucher: ${voucher.voucherNumber}');

      // Determine check type and company info from notes
      String checkType = 'own';
      String description = 'Check payment for voucher ${voucher.voucherNumber}';
      String? externalCompanyId;
      String? externalCompanyName;

      // Check if this is a cross-company payment by examining notes
      if (paymentTransaction.notes != null &&
          paymentTransaction.notes!.contains('Cross-company cheque:')) {
        checkType = 'other';
        description = paymentTransaction.notes!;

        // Extract company name from notes
        final regex = RegExp(r"Used (.+?)'s cheque");
        final match = regex.firstMatch(paymentTransaction.notes!);
        if (match != null) {
          externalCompanyName = match.group(1);
        }

        // Resolve external company UID from the account information
        if (paymentTransaction.accountId != null &&
            paymentTransaction.accountId!.isNotEmpty) {
          try {
            final accountDoc = await FirebaseFirestore.instance
                .collection('accounts')
                .doc(paymentTransaction.accountId!)
                .get();

            if (accountDoc.exists) {
              final accountData = accountDoc.data() as Map<String, dynamic>;
              externalCompanyId = accountData['uid'] as String?;
              log('Resolved external company UID: $externalCompanyId for account: ${paymentTransaction.accountId}');
            } else {
              log('Account not found for ID: ${paymentTransaction.accountId}');
            }
          } catch (e) {
            log('Error resolving external company UID: $e');
          }
        }
      }

      final checkUsageRecord = CheckUsageModel(
        id: const Uuid().v4(),
        uid: '', // Will be set by the service
        checkNumber: paymentTransaction.checkNumber!,
        bankName: paymentTransaction.bankName ?? '',
        accountId: paymentTransaction.accountId ?? '',
        accountName: paymentTransaction.accountName ?? '',
        voucherId: voucher.voucherNumber,
        voucherNumber: voucher.voucherNumber,
        amount: paymentTransaction.amount,
        issueDate: paymentTransaction.checkIssueDate ??
            paymentTransaction.transactionDate,
        expiryDate: paymentTransaction.checkExpiryDate,
        usageDate: paymentTransaction.transactionDate,
        payeeName: voucher.driverName.isNotEmpty
            ? voucher.driverName
            : 'Driver Payment',
        notes: paymentTransaction.notes ?? description,
        status: 'issued',
        checkType: checkType,
        externalCompanyId: externalCompanyId,
        externalCompanyName: externalCompanyName,
      );

      final result =
          await checkUsageRepository.createCheckUsage(checkUsageRecord);
      result.fold(
        (failure) => {
          log('Failed to create check usage record: $failure'),
          SnackbarUtils.showError(
              'Check Usage Error', 'Failed to record check usage: $failure'),
        },
        (_) => {
          log('Successfully created check usage record'),
        },
      );

      // If this is a cross-company check, create loan entry only
      // Financial records will be created after loan approval
      if (checkType == 'other' && externalCompanyName != null) {
        await _createCrossCompanyLoanEntry(paymentTransaction, voucher,
            externalCompanyName, externalCompanyId);

        log('Cross-company loan request created. Financial records will be created after approval.');
      }
    } catch (e) {
      log('Error creating check usage record: $e');
      SnackbarUtils.showError(
          'Check Usage Error', 'Error recording check usage: $e');
    }
  }

  // Create financial accounting entries for Company Freight, Tax, and Profit
  Future<void> _createFinancialAccountingEntries(VoucherModel voucher) async {
    try {
      log('Creating financial accounting entries for voucher: ${voucher.voucherNumber}');

      final companyFreight =
          double.tryParse(companyFreightController.text) ?? 0.0;
      if (companyFreight <= 0) {
        log('No company freight amount to process');
        return;
      }

      // 1. Company Freight (NLC Amount) - DEBIT entry
      await _createCompanyFreightEntry(voucher, companyFreight);

      // 2. Tax Account (4.6% of company freight) - DEBIT entry
      await _createTaxEntry(voucher, companyFreight);

      // 3. Freight Tax Account (15% of company freight) - DEBIT entry
      await _createFreightTaxEntry(voucher, companyFreight);

      // 4. Profit Account (calculated net profit) - CREDIT entry
      await _createProfitEntry(voucher, companyFreight);
    } catch (e) {
      log('Error creating financial accounting entries: $e');
      SnackbarUtils.showError(
        'Accounting Error',
        'Error creating financial accounting entries: $e',
      );
    }
  }

  // Create Company Freight accounting entry
  Future<void> _createCompanyFreightEntry(
      VoucherModel voucher, double companyFreight) async {
    // Check Chart of Accounts first, then legacy account
    final companyFreightChartAccount = selectedCompanyFreightChartAccount.value;
    final companyFreightAccount = selectedCompanyFreightAccount.value;

    if (companyFreightChartAccount == null &&
        (companyFreightAccount == null || companyFreightAccount.id.isEmpty)) {
      log('ERROR: Company freight account not selected');
      SnackbarUtils.showError(
          'Account Error', 'Please select a company freight account');
      return;
    }

    // If Chart of Accounts is selected, journal entries will be created automatically
    if (companyFreightChartAccount != null) {
      log('Chart of Accounts selected for company freight: $companyFreight to account: ${companyFreightChartAccount.displayName}');
      log('Journal entries will be created automatically by VoucherAccountingHookService');
      return;
    }

    // Legacy account handling (for backward compatibility)
    if (companyFreightAccount == null || companyFreightAccount.id.isEmpty) {
      log('ERROR: Company freight account not selected');
      SnackbarUtils.showError(
          'Account Error', 'Please select a company freight account');
      return;
    }

    log('Creating company freight entry: $companyFreight to account: ${companyFreightAccount.name}');

    final depositRepository = DepositRepositoryImpl(DepositFirebaseService());
    final referenceNumber = 'V-${voucher.voucherNumber}';

    // Check if deposit already exists for this voucher
    final existingDepositsResult = await depositRepository.getDeposits();
    DepositModel? existingDeposit;

    existingDepositsResult.fold(
      (failure) => log('Failed to fetch existing deposits: ${failure.message}'),
      (deposits) {
        existingDeposit = deposits.firstWhereOrNull((deposit) =>
            deposit.referenceNumber == referenceNumber &&
            deposit.categoryId == 'company_freight');
      },
    );

    if (existingDeposit != null) {
      // Update existing deposit by deleting and recreating
      log('Updating existing company freight deposit: ${existingDeposit!.id}');
      await depositRepository.deleteDeposit(existingDeposit!.id,
          existingDeposit!.accountId, existingDeposit!.amount);
    }

    final companyFreightDeposit = DepositModel(
      id: '', // Will be generated by Firebase
      accountId: companyFreightAccount.id,
      accountName: companyFreightAccount.name,
      amount: companyFreight,
      createdAt: parseDepartureDate(voucher.departureDate),
      categoryId: 'company_freight',
      categoryName: 'Company Freight',
      payerId: '',
      payerName: 'NLC Amount',
      referenceNumber: referenceNumber,
      notes: 'Company Freight V-${voucher.voucherNumber} - NLC Amount',
    );

    final result = await depositRepository.createDeposit(companyFreightDeposit);
    result.fold(
      (failure) => {
        log('Failed to create company freight entry: ${failure.message}'),
        SnackbarUtils.showError('Company Freight Error',
            'Failed to create company freight entry: ${failure.message}'),
      },
      (success) => {
        log('Successfully created company freight entry'),
      },
    );
  }

  // Create Tax accounting entry
  Future<void> _createTaxEntry(
      VoucherModel voucher, double companyFreight) async {
    final taxAccount = selectedSalesTaxAccount.value;
    if (taxAccount == null || taxAccount.id.isEmpty) {
      log('ERROR: Tax account not selected');
      SnackbarUtils.showError('Account Error', 'Please select a tax account');
      return;
    }

    final taxAmount = companyFreight * 0.046; // 4.6% tax
    log('Creating tax entry: $taxAmount to account: ${taxAccount.name}');

    final depositRepository = DepositRepositoryImpl(DepositFirebaseService());
    final referenceNumber = 'V-${voucher.voucherNumber}';

    // Check if deposit already exists for this voucher
    final existingDepositsResult = await depositRepository.getDeposits();
    DepositModel? existingDeposit;

    existingDepositsResult.fold(
      (failure) => log('Failed to fetch existing deposits: ${failure.message}'),
      (deposits) {
        existingDeposit = deposits.firstWhereOrNull((deposit) =>
            deposit.referenceNumber == referenceNumber &&
            deposit.categoryId == 'sales_tax');
      },
    );

    if (existingDeposit != null) {
      // Update existing deposit by deleting and recreating
      log('Updating existing tax deposit: ${existingDeposit!.id}');
      await depositRepository.deleteDeposit(existingDeposit!.id,
          existingDeposit!.accountId, existingDeposit!.amount);
    }

    final taxDeposit = DepositModel(
      id: '', // Will be generated by Firebase
      accountId: taxAccount.id,
      accountName: taxAccount.name,
      amount: taxAmount,
      createdAt: parseDepartureDate(voucher.departureDate),
      categoryId: 'sales_tax',
      categoryName: 'Sales Tax',
      payerId: '',
      payerName: 'Tax Collection',
      referenceNumber: referenceNumber,
      notes: 'Tax (4.6%) V-${voucher.voucherNumber}',
    );

    final result = await depositRepository.createDeposit(taxDeposit);
    result.fold(
      (failure) => {
        log('Failed to create tax entry: ${failure.message}'),
        SnackbarUtils.showError(
            'Tax Error', 'Failed to create tax entry: ${failure.message}'),
      },
      (success) => {
        log('Successfully created tax entry'),
      },
    );
  }

  // Create Freight Tax accounting entry with Tax Authority Distribution
  Future<void> _createFreightTaxEntry(
      VoucherModel voucher, double companyFreight) async {
    final freightTaxAccount = selectedFreightTaxAccount.value;
    if (freightTaxAccount == null || freightTaxAccount.id.isEmpty) {
      log('ERROR: Freight tax account not selected');
      SnackbarUtils.showError(
          'Account Error', 'Please select a freight tax account');
      return;
    }

    // Check if tax authorities are selected
    if (selectedTaxAuthorities.isEmpty) {
      log('WARNING: No tax authorities selected for 15% tax distribution');
      SnackbarUtils.showError('Tax Authority Error',
          'Please select at least one tax authority for 15% tax distribution');
      return;
    }

    final totalTaxAmount = companyFreight * 0.15; // 15% tax
    log('Creating freight tax entries for ${selectedTaxAuthorities.length} tax authorities. Total amount: $totalTaxAmount');

    // Create tax authority payment distribution
    await _createTaxAuthorityPayments(voucher, totalTaxAmount);
  }

  // Create Tax Authority Payment Distribution
  Future<void> _createTaxAuthorityPayments(
      VoucherModel voucher, double totalTaxAmount) async {
    final referenceNumber = 'V-${voucher.voucherNumber}';

    // Clean up existing tax authority deposits for this voucher
    await _cleanupExistingTaxAuthorityDeposits(referenceNumber);

    // Calculate payment distribution
    final selectedAuthorities = selectedTaxAuthorities.toList();
    final numberOfAuthorities = selectedAuthorities.length;

    if (numberOfAuthorities == 1) {
      // Single authority gets full 15% tax amount
      await _createSingleTaxAuthorityPayment(
          voucher, selectedAuthorities.first, totalTaxAmount, referenceNumber);
    } else if (numberOfAuthorities == 2) {
      // Dual authorities split the 15% tax amount equally (7.5% each)
      final splitAmount = totalTaxAmount / 2;
      for (final authority in selectedAuthorities) {
        await _createDualTaxAuthorityPayment(
            voucher, authority, splitAmount, referenceNumber);
      }
    } else {
      log('ERROR: Invalid number of tax authorities selected: $numberOfAuthorities');
      SnackbarUtils.showError('Tax Authority Error',
          'Invalid number of tax authorities selected. Please select 1 or 2 authorities.');
      return;
    }

    log('Successfully created tax authority payment distribution for ${selectedAuthorities.length} authorities');
  }

  // Clean up existing tax authority deposits for a voucher
  Future<void> _cleanupExistingTaxAuthorityDeposits(
      String referenceNumber) async {
    final depositRepository = DepositRepositoryImpl(DepositFirebaseService());

    try {
      final existingDepositsResult = await depositRepository.getDeposits();

      existingDepositsResult.fold(
        (failure) => log(
            'Failed to fetch existing deposits for cleanup: ${failure.message}'),
        (deposits) async {
          // Find all tax authority deposits for this voucher
          final taxAuthorityDeposits = deposits
              .where((deposit) =>
                  deposit.referenceNumber == referenceNumber &&
                  (deposit.categoryId == 'tax_authority_single' ||
                      deposit.categoryId == 'tax_authority_split'))
              .toList();

          // Delete existing tax authority deposits
          for (final deposit in taxAuthorityDeposits) {
            log('Cleaning up existing tax authority deposit: ${deposit.id}');
            await depositRepository.deleteDeposit(
                deposit.id, deposit.accountId, deposit.amount);
          }
        },
      );
    } catch (e) {
      log('Error during tax authority deposits cleanup: $e');
    }
  }

  // Create single tax authority payment (full 15% amount)
  Future<void> _createSingleTaxAuthorityPayment(VoucherModel voucher,
      String authority, double amount, String referenceNumber) async {
    final freightTaxAccount = selectedFreightTaxAccount.value;
    if (freightTaxAccount == null) return;

    final depositRepository = DepositRepositoryImpl(DepositFirebaseService());

    final taxAuthorityDeposit = DepositModel(
      id: '', // Will be generated by Firebase
      accountId: freightTaxAccount.id,
      accountName: freightTaxAccount.name,
      amount: amount,
      createdAt: parseDepartureDate(voucher.departureDate),
      categoryId: 'tax_authority_single',
      categoryName: 'Tax Authority Payment',
      payerId: '',
      payerName: authority,
      referenceNumber: referenceNumber,
      notes: 'Tax payment to $authority - Voucher #${voucher.voucherNumber}',
    );

    final result = await depositRepository.createDeposit(taxAuthorityDeposit);

    result.fold(
      (failure) => {
        log('Failed to create single tax authority payment: ${failure.message}'),
        SnackbarUtils.showError('Tax Payment Error',
            'Failed to create tax payment for $authority: ${failure.message}'),
      },
      (success) => {
        log('Successfully created single tax authority payment for $authority: \$${amount.toStringAsFixed(2)}'),
      },
    );
  }

  // Create dual tax authority payment (split 15% amount)
  Future<void> _createDualTaxAuthorityPayment(VoucherModel voucher,
      String authority, double amount, String referenceNumber) async {
    final freightTaxAccount = selectedFreightTaxAccount.value;
    if (freightTaxAccount == null) return;

    final depositRepository = DepositRepositoryImpl(DepositFirebaseService());

    final taxAuthorityDeposit = DepositModel(
      id: '', // Will be generated by Firebase
      accountId: freightTaxAccount.id,
      accountName: freightTaxAccount.name,
      amount: amount,
      createdAt: parseDepartureDate(voucher.departureDate),
      categoryId: 'tax_authority_split',
      categoryName: 'Tax Authority Payment (Split)',
      payerId: '',
      payerName: authority,
      referenceNumber: referenceNumber,
      notes:
          'Tax payment to $authority - Voucher #${voucher.voucherNumber} (Split payment)',
    );

    final result = await depositRepository.createDeposit(taxAuthorityDeposit);

    result.fold(
      (failure) => {
        log('Failed to create dual tax authority payment: ${failure.message}'),
        SnackbarUtils.showError('Tax Payment Error',
            'Failed to create split tax payment for $authority: ${failure.message}'),
      },
      (success) => {
        log('Successfully created dual tax authority payment for $authority: \$${amount.toStringAsFixed(2)}'),
      },
    );
  }

  // Create Profit accounting entry
  Future<void> _createProfitEntry(
      VoucherModel voucher, double companyFreight) async {
    final profitAccount = selectedProfitAccount.value;
    if (profitAccount == null || profitAccount.id.isEmpty) {
      log('ERROR: Profit account not selected');
      SnackbarUtils.showError(
          'Account Error', 'Please select a profit account');
      return;
    }

    // Calculate net profit: Company Freight - Tax Amount - Total Voucher Expenses
    final taxAmount = companyFreight * 0.046;
    final totalVoucherExpenses = calculatedTotalTruckFreight.value;
    final netProfit = companyFreight - taxAmount - totalVoucherExpenses;
    final referenceNumber = 'V-${voucher.voucherNumber}';

    log('Creating profit entry: $netProfit to account: ${profitAccount.name}');

    if (netProfit > 0) {
      // Positive profit - create as DEPOSIT (credit to profit account)
      final depositRepository = DepositRepositoryImpl(DepositFirebaseService());

      // Check if deposit already exists for this voucher
      final existingDepositsResult = await depositRepository.getDeposits();
      DepositModel? existingDeposit;

      existingDepositsResult.fold(
        (failure) =>
            log('Failed to fetch existing deposits: ${failure.message}'),
        (deposits) {
          existingDeposit = deposits.firstWhereOrNull((deposit) =>
              deposit.referenceNumber == referenceNumber &&
              deposit.categoryId == 'net_profit');
        },
      );

      if (existingDeposit != null) {
        // Update existing deposit by deleting and recreating
        log('Updating existing profit deposit: ${existingDeposit!.id}');
        await depositRepository.deleteDeposit(existingDeposit!.id,
            existingDeposit!.accountId, existingDeposit!.amount);
      }

      // Also check for existing loss expense and delete it if found
      final expenseRepository = ExpenseRepositoryImpl(
        ExpenseFirebaseService(),
        AccountFirebaseService(),
      );
      final existingExpensesResult = await expenseRepository.getExpenses();
      ExpenseModel? existingLossExpense;

      existingExpensesResult.fold(
        (failure) =>
            log('Failed to fetch existing expenses: ${failure.message}'),
        (expenses) {
          existingLossExpense = expenses.firstWhereOrNull((expense) =>
              expense.referenceNumber == referenceNumber &&
              expense.categoryId == 'net_loss');
        },
      );

      if (existingLossExpense != null) {
        log('Deleting existing loss expense: ${existingLossExpense!.id}');
        await expenseRepository.deleteExpense(existingLossExpense!.id,
            existingLossExpense!.accountId, existingLossExpense!.amount);
      }

      final profitDeposit = DepositModel(
        id: '', // Will be generated by Firebase
        accountId: profitAccount.id,
        accountName: profitAccount.name,
        amount: netProfit,
        createdAt: parseDepartureDate(voucher.departureDate),
        categoryId: 'net_profit',
        categoryName: 'Net Profit',
        payerId: '',
        payerName: 'Profit',
        referenceNumber: referenceNumber,
        notes: 'Net Profit V-${voucher.voucherNumber}',
      );

      final result = await depositRepository.createDeposit(profitDeposit);
      result.fold(
        (failure) => {
          log('Failed to create profit entry: ${failure.message}'),
          SnackbarUtils.showError('Profit Error',
              'Failed to create profit entry: ${failure.message}'),
        },
        (success) => {
          log('Successfully created profit entry'),
        },
      );
    } else {
      // Negative profit (loss) - create as EXPENSE (debit from profit account)
      final expenseRepository = ExpenseRepositoryImpl(
        ExpenseFirebaseService(),
        AccountFirebaseService(),
      );

      // Check if expense already exists for this voucher
      final existingExpensesResult = await expenseRepository.getExpenses();
      ExpenseModel? existingExpense;

      existingExpensesResult.fold(
        (failure) =>
            log('Failed to fetch existing expenses: ${failure.message}'),
        (expenses) {
          existingExpense = expenses.firstWhereOrNull((expense) =>
              expense.referenceNumber == referenceNumber &&
              expense.categoryId == 'net_loss');
        },
      );

      if (existingExpense != null) {
        // Update existing expense by deleting and recreating
        log('Updating existing loss expense: ${existingExpense!.id}');
        await expenseRepository.deleteExpense(existingExpense!.id,
            existingExpense!.accountId, existingExpense!.amount);
      }

      // Also check for existing profit deposit and delete it if found
      final depositRepository = DepositRepositoryImpl(DepositFirebaseService());
      final existingDepositsResult = await depositRepository.getDeposits();
      DepositModel? existingProfitDeposit;

      existingDepositsResult.fold(
        (failure) =>
            log('Failed to fetch existing deposits: ${failure.message}'),
        (deposits) {
          existingProfitDeposit = deposits.firstWhereOrNull((deposit) =>
              deposit.referenceNumber == referenceNumber &&
              deposit.categoryId == 'net_profit');
        },
      );

      if (existingProfitDeposit != null) {
        log('Deleting existing profit deposit: ${existingProfitDeposit!.id}');
        await depositRepository.deleteDeposit(existingProfitDeposit!.id,
            existingProfitDeposit!.accountId, existingProfitDeposit!.amount);
      }

      final lossExpense = ExpenseModel(
        id: '', // Will be generated by Firebase
        title: 'Net Loss V-${voucher.voucherNumber}',
        accountId: profitAccount.id,
        accountName: profitAccount.name,
        amount: netProfit.abs(), // Use absolute value for expense
        createdAt: parseDepartureDate(voucher.departureDate),
        categoryId: 'net_loss',
        categoryName: 'Net Loss',
        payeeId: '',
        payeeName: 'Loss',
        referenceNumber: referenceNumber,
        notes: 'Net Loss V-${voucher.voucherNumber}',
      );

      final result = await expenseRepository.createExpense(lossExpense);
      result.fold(
        (failure) => {
          log('Failed to create loss entry: ${failure.message}'),
          SnackbarUtils.showError(
              'Loss Error', 'Failed to create loss entry: ${failure.message}'),
        },
        (success) => {
          log('Successfully created loss entry'),
        },
      );
    }
  }

  // Create loan entry for cross-company check transactions
  Future<void> _createCrossCompanyLoanEntry(
    PaymentTransactionModel paymentTransaction,
    VoucherModel voucher,
    String externalCompanyName,
    String? externalCompanyId,
  ) async {
    try {
      log('Creating cross-company loan entry for check payment: ${paymentTransaction.amount}');

      // Get current user info
      final currentUser = companyController.company;

      // Get current user's company name from database
      String currentCompanyName = 'Current Company'; // Default fallback
      try {
        // Try to get the current user's company name from database
        final companyService = CompanyFirebaseService();
        final userResult = await companyService.getUserById(currentUser.uid);

        userResult.fold(
          (failure) {
            log('Could not get current user from database: ${failure.message}');
            // Fallback to Firebase Auth display name
            if (FirebaseAuth.instance.currentUser?.displayName != null) {
              currentCompanyName =
                  FirebaseAuth.instance.currentUser!.displayName!;
            }
          },
          (userModel) {
            currentCompanyName = userModel.companyName;
            log('Retrieved current company name from database: $currentCompanyName');
          },
        );
      } catch (e) {
        log('Error getting current user company name: $e');
        // Fallback to Firebase Auth display name
        try {
          if (FirebaseAuth.instance.currentUser?.displayName != null) {
            currentCompanyName =
                FirebaseAuth.instance.currentUser!.displayName!;
          }
        } catch (authError) {
          log('Could not get current user display name: $authError');
        }
      }

      // Get current user's accounts to find a suitable account for the loan
      final accountService = AccountFirebaseService();
      final currentUserAccounts = await accountService.getAccounts();

      if (currentUserAccounts.isEmpty) {
        log('No accounts found for current user - cannot create loan entry');
        SnackbarUtils.showWarning(
          'Loan Entry Warning',
          'No accounts available for loan entry. Cross-company check payment recorded without loan tracking.',
        );
        return;
      }

      // Use the first available account as the borrower account
      final borrowerAccount = currentUserAccounts.first;

      // Create loan model - the external company is lending to current company
      final loan = LoanModel(
        id: '', // Will be generated by Firebase
        uid: currentUser.uid, // Current company's UID
        requestedBy: currentUser.uid, // Current company is requesting
        requestedByName: currentCompanyName, // Current company name
        requestedTo:
            externalCompanyId ?? '', // External company ID (if available)
        requestedToName: externalCompanyName, // External company name
        fromAccountId:
            paymentTransaction.accountId ?? '', // External company's account
        toAccountId: borrowerAccount.id, // Current company's account (borrower)
        fromAccountName: paymentTransaction.accountName ??
            '', // External company's account name
        toAccountName: borrowerAccount.name, // Current company's account name
        amount: paymentTransaction.amount,
        dueDate:
            DateTime.now().add(const Duration(days: 30)), // Default 30 days
        status: 'pending', // Pending approval from external company
        requestDate: paymentTransaction.transactionDate,
        notes:
            'Cross-company check usage request for voucher ${voucher.voucherNumber}. Check #${paymentTransaction.checkNumber ?? 'Unknown'}. Amount: ${paymentTransaction.amount}. ${paymentTransaction.notes ?? ''}',
      );

      // Create pending loan request that requires approval from external company
      await _createPendingLoanRequest(
          loan, externalCompanyName, voucher, paymentTransaction);

      log('Successfully created pending loan request');
      SnackbarUtils.showSuccess(
        'Loan Request Sent',
        'Check usage request sent to $externalCompanyName for approval. Voucher will be completed after approval.',
      );
    } catch (e) {
      log('Error creating cross-company loan entry: $e');
      SnackbarUtils.showError(
        'Loan Entry Error',
        'Error creating loan entry for cross-company check: $e',
      );
    }
  }

  /// Create pending loan request for cross-company check usage
  /// This creates a proper loan request that requires approval from the external company
  Future<void> _createPendingLoanRequest(
    LoanModel loan,
    String externalCompanyName,
    VoucherModel voucher,
    PaymentTransactionModel paymentTransaction,
  ) async {
    try {
      log('Creating pending loan request for cross-company check usage');

      // Create a proper loan request with pending status
      final loanRequest = loan.copyWith(
        id: const Uuid().v4(), // Generate unique ID
        status: 'pending', // Requires approval from external company
        // Keep the original requestDate from paymentTransaction.transactionDate
        // No approval date yet - will be set when approved
      );

      // Save loan request to Firestore loans collection
      await FirebaseFirestore.instance
          .collection('loans')
          .doc(loanRequest.id)
          .set(loanRequest.toJson());

      // Store the voucher ID in the loan request for reference
      await FirebaseFirestore.instance
          .collection('loans')
          .doc(loanRequest.id)
          .update({
        'voucherId': voucher.voucherNumber,
        'voucherReference': 'V-${voucher.voucherNumber}',
        'checkNumber': paymentTransaction.checkNumber ?? '',
      });

      log('Pending loan request created successfully: ${loanRequest.id}');
      log('External company $externalCompanyName will need to approve this request');
    } catch (e) {
      log('Error creating pending loan request: $e');
      // Don't throw error - this is supplementary tracking
      // The main voucher creation should not fail because of this
      SnackbarUtils.showWarning(
        'Loan Request Warning',
        'Could not create loan request. Please contact $externalCompanyName directly.',
      );
    }
  }

  void showErrorDialog(FailureObj failure) {
    Get.dialog(
      AlertDialog(
        title: Text(AppStrings.errorS),
        content: Text(failure.message),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text("OK"),
          ),
        ],
      ),
    );
  }

  void displaySuccessSnackbar(SuccessObj success) {
    SnackbarUtils.showSuccess(AppStrings.success, success.message);

    // Navigate back to the previous screen after a short delay
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (Get.isDialogOpen == true) {
        Get.back();
      }
      Get.back();
    });
  }

  /// Handle broker fees for edit mode - update existing or create new
  Future<void> _handleBrokerFeesForEdit(VoucherModel voucher) async {
    final brokerAccount = selectedBrokerAccount.value;
    if (brokerAccount == null || brokerAccount.id.isEmpty) return;

    final referenceNumber = 'V-${voucher.voucherNumber}';
    log(voucher.brokerType);
    if (voucher.brokerType == BrokerType.own.value) {
      // Own broker fees → DEPOSIT (money coming IN to broker account)
      log('Handling own broker fees deposit: ${voucher.brokerFees} to account: ${brokerAccount.name}');

      final depositRepository = DepositRepositoryImpl(DepositFirebaseService());

      // Check if deposit already exists for this voucher
      final existingDepositsResult = await depositRepository.getDeposits();
      DepositModel? existingDeposit;

      existingDepositsResult.fold(
        (failure) =>
            log('Failed to fetch existing deposits: ${failure.message}'),
        (deposits) {
          existingDeposit = deposits.firstWhereOrNull((deposit) =>
              deposit.referenceNumber == referenceNumber &&
              deposit.categoryId == 'broker_fees');
        },
      );

      if (existingDeposit != null) {
        // Update existing deposit by deleting and recreating
        log('Updating existing broker fees deposit: ${existingDeposit!.id}');
        await depositRepository.deleteDeposit(existingDeposit!.id,
            existingDeposit!.accountId, existingDeposit!.amount);
      }

      final brokerDeposit = DepositModel(
        id: '', // Will be generated by Firebase
        accountId: brokerAccount.id,
        accountName: brokerAccount.name,
        amount: voucher.brokerFees,
        createdAt: parseDepartureDate(voucher.departureDate),
        categoryId: 'broker_fees',
        categoryName: 'Broker Fees',
        payerId: '',
        payerName:
            voucher.brokerName.isNotEmpty ? voucher.brokerName : 'Own Broker',
        referenceNumber: referenceNumber,
        notes: 'Own broker fees for voucher $referenceNumber',
      );

      final depositResult =
          await depositRepository.createDeposit(brokerDeposit);
      depositResult.fold(
        (failure) =>
            log('Failed to create own broker fees deposit: ${failure.message}'),
        (success) => log('Successfully created own broker fees deposit'),
      );
    } else if (voucher.brokerType == BrokerType.outsource.value) {
      // Outsource broker fees → EXPENSE (money going OUT from broker account)
      log('Handling outsource broker fees expense: ${voucher.brokerFees} from account: ${brokerAccount.name}');

      final expenseRepository = ExpenseRepositoryImpl(
        ExpenseFirebaseService(),
        AccountFirebaseService(),
      );

      // Check if expense already exists for this voucher
      final existingExpensesResult = await expenseRepository.getExpenses();
      ExpenseModel? existingExpense;

      existingExpensesResult.fold(
        (failure) =>
            log('Failed to fetch existing expenses: ${failure.message}'),
        (expenses) {
          existingExpense = expenses.firstWhereOrNull((expense) =>
              expense.referenceNumber == referenceNumber &&
              expense.categoryId == 'broker_fees');
        },
      );

      if (existingExpense != null) {
        // Update existing expense by deleting and recreating
        log('Updating existing broker fees expense: ${existingExpense!.id}');
        await expenseRepository.deleteExpense(existingExpense!.id,
            existingExpense!.accountId, existingExpense!.amount);
      }

      final brokerExpense = ExpenseModel(
        id: '', // Will be generated by Firebase
        title: 'Outsource Broker Fees - $referenceNumber',
        accountId: brokerAccount.id,
        accountName: brokerAccount.name,
        amount: voucher.brokerFees,
        createdAt: parseDepartureDate(voucher.departureDate),
        categoryId: 'broker_fees',
        categoryName: 'Broker Fees',
        payeeId: '',
        payeeName: voucher.brokerName.isNotEmpty
            ? voucher.brokerName
            : 'Outsource Broker',
        referenceNumber: referenceNumber,
        notes: 'Outsource broker fees for voucher $referenceNumber',
      );

      final expenseResult =
          await expenseRepository.createExpense(brokerExpense);
      expenseResult.fold(
        (failure) => log(
            'Failed to create outsource broker fees expense: ${failure.message}'),
        (success) => log('Successfully created outsource broker fees expense'),
      );
    }
  }

  /// Handle munshiana fees for edit mode - update existing or create new
  Future<void> _handleMunshianaFeesForEdit(VoucherModel voucher) async {
    final munshianaAccount = selectedMunshianaAccount.value;
    if (munshianaAccount == null || munshianaAccount.id.isEmpty) return;

    final referenceNumber = 'V-${voucher.voucherNumber}';

    // Munshiana fees → DEPOSIT (money coming IN to munshiana account)
    log('Handling munshiana fees deposit: ${voucher.munshianaFees} to account: ${munshianaAccount.name}');

    final depositRepository = DepositRepositoryImpl(DepositFirebaseService());

    // Check if deposit already exists for this voucher
    final existingDepositsResult = await depositRepository.getDeposits();
    DepositModel? existingDeposit;

    existingDepositsResult.fold(
      (failure) => log('Failed to fetch existing deposits: ${failure.message}'),
      (deposits) {
        existingDeposit = deposits.firstWhereOrNull((deposit) =>
            deposit.referenceNumber == referenceNumber &&
            deposit.categoryId == 'munshiana');
      },
    );

    if (existingDeposit != null) {
      // Update existing deposit by deleting and recreating
      log('Updating existing munshiana fees deposit: ${existingDeposit!.id}');
      await depositRepository.deleteDeposit(existingDeposit!.id,
          existingDeposit!.accountId, existingDeposit!.amount);
    }

    final munshianaDeposit = DepositModel(
      id: '', // Will be generated by Firebase
      accountId: munshianaAccount.id,
      accountName: munshianaAccount.name,
      amount: voucher.munshianaFees,
      createdAt: parseDepartureDate(voucher.departureDate),
      categoryId: 'munshiana',
      categoryName: 'Munshiana',
      payerId: '',
      payerName: 'Munshiana Payment',
      referenceNumber: referenceNumber,
      notes: 'Munshiana payment for voucher $referenceNumber',
    );

    final depositResult =
        await depositRepository.createDeposit(munshianaDeposit);
    depositResult.fold(
      (failure) =>
          log('Failed to create munshiana deposit: ${failure.message}'),
      (success) => log('Successfully created munshiana deposit'),
    );
  }

  /// Validate Chart of Accounts selections for voucher
  ValidationResult validateChartOfAccountsSelections(
      {bool strictValidation = false}) {
    // Use enhanced validation if we have access to all accounts
    try {
      final allAccounts = allChartOfAccounts;
      final totalAmount = _calculateTotalVoucherAmount();

      if (allAccounts.isNotEmpty) {
        return VoucherChartOfAccountsService.validateVoucherAccountsEnhanced(
          brokerAccount: selectedBrokerChartAccount.value,
          munshianaAccount: selectedMunshianaChartAccount.value,
          salesTaxAccount: selectedSalesTaxChartAccount.value,
          freightTaxAccount: selectedFreightTaxChartAccount.value,
          profitAccount: selectedProfitChartAccount.value,
          paymentTransactions: localPaymentTransactions,
          allAccounts: allAccounts,
          totalVoucherAmount: totalAmount,
          strictValidation: strictValidation,
        );
      }
    } catch (e) {
      log('Error in enhanced validation, falling back to basic validation: $e');
    }

    // Fallback to basic validation
    return VoucherChartOfAccountsService.validateVoucherAccounts(
      brokerAccount: selectedBrokerChartAccount.value,
      munshianaAccount: selectedMunshianaChartAccount.value,
      salesTaxAccount: selectedSalesTaxChartAccount.value,
      freightTaxAccount: selectedFreightTaxChartAccount.value,
      profitAccount: selectedProfitChartAccount.value,
      paymentTransactions: localPaymentTransactions,
    );
  }

  /// Calculate total voucher amount for validation purposes
  double _calculateTotalVoucherAmount() {
    double total = 0.0;

    // Add broker fees
    final brokerFeesAmount = double.tryParse(brokerFeesController.text) ?? 0.0;
    if (brokerFeesAmount > 0) total += brokerFeesAmount;

    // Add munshiana fees
    final munshianaFeesAmount =
        double.tryParse(munshianaFeesController.text) ?? 0.0;
    if (munshianaFeesAmount > 0) total += munshianaFeesAmount;

    // Add calculated tax amounts
    if (calculatedSalesTax.value > 0) total += calculatedSalesTax.value;
    if (calculatedFreightTax.value > 0) total += calculatedFreightTax.value;

    // Add company freight amount
    final companyFreightAmount =
        double.tryParse(companyFreightController.text) ?? 0.0;
    if (companyFreightAmount > 0) total += companyFreightAmount;

    // Add payment transaction amounts
    for (final payment in localPaymentTransactions) {
      total += payment.amount;
    }

    return total;
  }

  /// Get all Chart of Accounts for dropdown selection
  List<ChartOfAccountsModel> get allChartOfAccounts {
    try {
      final controller = Get.find<ChartOfAccountsController>();
      return controller.accounts;
    } catch (e) {
      log('Error getting Chart of Accounts: $e');
      return [];
    }
  }

  /// Set broker Chart of Accounts selection
  void setBrokerChartAccount(ChartOfAccountsModel? account) {
    selectedBrokerChartAccount.value = account;
    VoucherChartOfAccountsService.logAccountSelection(
      voucherNumber: voucherNumberController.text,
      brokerAccount: account,
    );
  }

  /// Set munshiana Chart of Accounts selection
  void setMunshianaChartAccount(ChartOfAccountsModel? account) {
    selectedMunshianaChartAccount.value = account;
    VoucherChartOfAccountsService.logAccountSelection(
      voucherNumber: voucherNumberController.text,
      munshianaAccount: account,
    );
  }

  /// Set sales tax Chart of Accounts selection
  void setSalesTaxChartAccount(ChartOfAccountsModel? account) {
    selectedSalesTaxChartAccount.value = account;
    VoucherChartOfAccountsService.logAccountSelection(
      voucherNumber: voucherNumberController.text,
      salesTaxAccount: account,
    );
  }

  /// Set freight tax Chart of Accounts selection
  void setFreightTaxChartAccount(ChartOfAccountsModel? account) {
    selectedFreightTaxChartAccount.value = account;
    VoucherChartOfAccountsService.logAccountSelection(
      voucherNumber: voucherNumberController.text,
      freightTaxAccount: account,
    );
  }

  /// Set profit Chart of Accounts selection
  void setProfitChartAccount(ChartOfAccountsModel? account) {
    selectedProfitChartAccount.value = account;
    VoucherChartOfAccountsService.logAccountSelection(
      voucherNumber: voucherNumberController.text,
      profitAccount: account,
    );
  }

  /// Set company freight Chart of Accounts selection
  void setCompanyFreightChartAccount(ChartOfAccountsModel? account) {
    selectedCompanyFreightChartAccount.value = account;
    VoucherChartOfAccountsService.logAccountSelection(
      voucherNumber: voucherNumberController.text,
      companyFreightAccount: account,
    );
  }

  /// Set truck freight Chart of Accounts selection
  void setTruckFreightChartAccount(ChartOfAccountsModel? account) {
    selectedTruckFreightChartAccount.value = account;
    VoucherChartOfAccountsService.logAccountSelection(
      voucherNumber: voucherNumberController.text,
      truckFreightAccount: account,
    );
  }
}

class TextFieldTagsController extends TextEditingController {
  List<String>? getTags;

  void onTagSubmitted(String tag) {
    getTags ??= [];
    if (!getTags!.contains(tag)) {
      getTags!.add(tag);
    }
  }

  void removeTag(String tag) {
    getTags?.remove(tag);
  }

  void clearTags() {
    getTags?.clear();
  }
}
