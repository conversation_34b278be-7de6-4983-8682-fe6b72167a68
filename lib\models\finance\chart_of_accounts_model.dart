import 'package:flutter/material.dart';

enum AccountCategory {
  assets('Assets', 1000, 1999),
  liabilities('Liabilities', 2000, 2999),
  equity('Equity', 3000, 3999),
  revenue('Revenue', 4000, 4999),
  expenses('Expenses', 5000, 5999);

  const AccountCategory(this.displayName, this.startRange, this.endRange);

  final String displayName;
  final int startRange;
  final int endRange;

  /// Get the color associated with this account category
  Color get color {
    switch (this) {
      case AccountCategory.assets:
        return const Color(0xFF4CAF50); // Green
      case AccountCategory.liabilities:
        return const Color(0xFFF44336); // Red
      case AccountCategory.equity:
        return const Color(0xFF2196F3); // Blue
      case AccountCategory.revenue:
        return const Color(0xFF9C27B0); // Purple
      case AccountCategory.expenses:
        return const Color(0xFFFF9800); // Orange
    }
  }

  /// Get the icon associated with this account category
  IconData get icon {
    switch (this) {
      case AccountCategory.assets:
        return Icons.account_balance_wallet;
      case AccountCategory.liabilities:
        return Icons.credit_card;
      case AccountCategory.equity:
        return Icons.business;
      case AccountCategory.revenue:
        return Icons.trending_up;
      case AccountCategory.expenses:
        return Icons.trending_down;
    }
  }

  /// Get the account number range for this category
  ({int start, int end}) get accountNumberRange {
    return (start: startRange, end: endRange);
  }

  static AccountCategory fromString(String value) {
    return AccountCategory.values.firstWhere(
      (category) => category.name == value,
      orElse: () => AccountCategory.assets,
    );
  }
}

enum AccountType {
  // Asset Types
  cash('Cash', AccountCategory.assets),
  bank('Bank', AccountCategory.assets),
  accountsReceivable('Accounts Receivable', AccountCategory.assets),
  inventory('Inventory', AccountCategory.assets),
  fixedAssets('Fixed Assets', AccountCategory.assets),
  currentAssets('Current Assets', AccountCategory.assets),

  // Liability Types
  accountsPayable('Accounts Payable', AccountCategory.liabilities),
  loansPayable('Loans Payable', AccountCategory.liabilities),
  currentLiabilities('Current Liabilities', AccountCategory.liabilities),
  longTermLiabilities('Long Term Liabilities', AccountCategory.liabilities),

  // Equity Types
  ownersEquity('Owner\'s Equity', AccountCategory.equity),
  retainedEarnings('Retained Earnings', AccountCategory.equity),
  equityServiceRevenue('Service Revenue', AccountCategory.equity),

  // Revenue Types
  salesRevenue('Sales Revenue', AccountCategory.revenue),
  serviceRevenue('Service Revenue', AccountCategory.revenue),
  otherRevenue('Other Revenue', AccountCategory.revenue),

  // Expense Types
  operatingExpenses('Operating Expenses', AccountCategory.expenses),
  administrativeExpenses('Administrative Expenses', AccountCategory.expenses),
  interestExpense('Interest Expense', AccountCategory.expenses),
  taxExpense('Tax Expense', AccountCategory.expenses);

  const AccountType(this.displayName, this.category);

  final String displayName;
  final AccountCategory category;

  static AccountType fromString(String value) {
    return AccountType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => AccountType.cash,
    );
  }

  static List<AccountType> getTypesForCategory(AccountCategory category) {
    return AccountType.values
        .where((type) => type.category == category)
        .toList();
  }
}

class ChartOfAccountsModel {
  final String id;
  final String accountNumber;
  final String accountName;
  final String? description;
  final AccountCategory category;
  final AccountType accountType;
  final String? parentAccountId; // For hierarchical structure
  final List<String> childAccountIds; // Child account IDs
  final bool isActive;
  final double balance; // Current account balance
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String uid; // Company isolation

  ChartOfAccountsModel({
    required this.id,
    required this.accountNumber,
    required this.accountName,
    this.description,
    required this.category,
    required this.accountType,
    this.parentAccountId,
    this.childAccountIds = const [],
    this.isActive = true,
    this.balance = 0.0,
    required this.createdAt,
    this.updatedAt,
    required this.uid,
  });

  // Getter for backward compatibility
  AccountCategory get accountCategory => category;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'description': description,
      'category': category.name,
      'accountType': accountType.name,
      'parentAccountId': parentAccountId,
      'childAccountIds': childAccountIds,
      'isActive': isActive,
      'balance': balance,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'uid': uid,
    };
  }

  factory ChartOfAccountsModel.fromJson(Map<String, dynamic> json) {
    return ChartOfAccountsModel(
      id: json['id'] ?? '',
      accountNumber: json['accountNumber'] ?? '',
      accountName: json['accountName'] ?? '',
      description: json['description'],
      category: AccountCategory.fromString(json['category'] ?? 'assets'),
      accountType: AccountType.fromString(json['accountType'] ?? 'cash'),
      parentAccountId: json['parentAccountId'],
      childAccountIds: List<String>.from(json['childAccountIds'] ?? []),
      isActive: json['isActive'] ?? true,
      balance: (json['balance'] ?? 0.0).toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
      uid: json['uid'] ?? '',
    );
  }

  ChartOfAccountsModel copyWith({
    String? id,
    String? accountNumber,
    String? accountName,
    String? description,
    AccountCategory? category,
    AccountType? accountType,
    String? parentAccountId,
    List<String>? childAccountIds,
    bool? isActive,
    double? balance,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uid,
  }) {
    return ChartOfAccountsModel(
      id: id ?? this.id,
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
      description: description ?? this.description,
      category: category ?? this.category,
      accountType: accountType ?? this.accountType,
      parentAccountId: parentAccountId ?? this.parentAccountId,
      childAccountIds: childAccountIds ?? this.childAccountIds,
      isActive: isActive ?? this.isActive,
      balance: balance ?? this.balance,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uid: uid ?? this.uid,
    );
  }

  // Helper methods
  bool get hasChildren => childAccountIds.isNotEmpty;
  bool get hasParent => parentAccountId != null;
  int get level =>
      parentAccountId == null ? 0 : 1; // Can be enhanced for deeper nesting

  String get fullAccountNumber {
    // Format: Category prefix + account number (e.g., 1001, 2001, etc.)
    return accountNumber;
  }

  String get displayName => '$accountNumber - $accountName';

  @override
  String toString() {
    return 'ChartOfAccountsModel(id: $id, accountNumber: $accountNumber, accountName: $accountName, category: ${category.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChartOfAccountsModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
