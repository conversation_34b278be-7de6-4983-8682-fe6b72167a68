import 'dart:developer';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/expense_model.dart';
import '../../models/finance/bill_model.dart';
import '../../models/voucher_model.dart';
import '../../models/finance/loan_model.dart';
import '../../models/finance/account_transaction_model.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';

/// Service for automatically generating journal entries from existing transactions
class AutomaticJournalEntryService {
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  AutomaticJournalEntryService(
    this._chartOfAccountsService,
  );

  /// Generate journal entry from expense transaction
  Future<JournalEntryModel?> generateExpenseJournalEntry({
    required ExpenseModel expense,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get expense account (should be an expense account)
      final expenseAccount = await _getAccountByName(expense.categoryName, uid);
      if (expenseAccount == null) return null;

      // Get cash/bank account (should be an asset account)
      final cashAccount = await _getAccountByName(expense.accountName, uid);
      if (cashAccount == null) return null;

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[
        // Debit expense account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: expenseAccount.id,
          accountName: expenseAccount.accountName,
          accountNumber: expenseAccount.accountNumber,
          description: 'Expense: ${expense.title}',
          debitAmount: expense.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit cash/bank account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Payment for: ${expense.title}',
          debitAmount: 0.0,
          creditAmount: expense.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: expense.createdAt,
        description: 'Expense: ${expense.title} - ${expense.payeeName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: expense.amount,
        totalCredits: expense.amount,
        referenceNumber: expense.referenceNumber,
        sourceTransactionId: expense.id,
        sourceTransactionType: 'expense',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating expense journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from bill transaction
  Future<JournalEntryModel?> generateBillJournalEntry({
    required BillModel bill,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get accounts payable account
      final accountsPayableAccount = await _getAccountByType(
        AccountType.currentLiabilities,
        'Accounts Payable',
        uid,
      );
      if (accountsPayableAccount == null) return null;

      // Get revenue/service account
      final revenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Service Revenue',
        uid,
      );
      if (revenueAccount == null) return null;

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[
        // Debit accounts receivable (if bill represents revenue)
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: accountsPayableAccount.id,
          accountName: accountsPayableAccount.accountName,
          accountNumber: accountsPayableAccount.accountNumber,
          description: 'Bill: ${bill.billNumber}',
          debitAmount: bill.totalAmount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit revenue account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: revenueAccount.id,
          accountName: revenueAccount.accountName,
          accountNumber: revenueAccount.accountNumber,
          description: 'Revenue from bill: ${bill.billNumber}',
          debitAmount: 0.0,
          creditAmount: bill.totalAmount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: bill.billDate,
        description:
            'Bill: ${bill.billNumber} - ${bill.customerName ?? "Multiple Customers"}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: bill.totalAmount,
        totalCredits: bill.totalAmount,
        referenceNumber: bill.billNumber,
        sourceTransactionId: bill.billId,
        sourceTransactionType: 'bill',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating bill journal entry: $e');
      return null;
    }
  }

  /// Generate single journal entry from voucher transaction with multiple line items
  Future<List<JournalEntryModel>> generateVoucherJournalEntries({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      log('Generating single journal entry for voucher: ${voucher.voucherNumber}');

      // Generate single journal entry with all line items
      final journalEntry = await _generateSingleVoucherJournalEntry(
        voucher: voucher,
        uid: uid,
        createdBy: createdBy,
      );

      if (journalEntry != null) {
        log('Successfully generated journal entry with ${journalEntry.lines.length} line items');
        return [journalEntry]; // Return single entry in list
      } else {
        log('No journal entry generated for voucher: ${voucher.voucherNumber}');
        return [];
      }
    } catch (e) {
      log('Error generating voucher journal entries: $e');
      return [];
    }
  }

  /// Generate a single journal entry with multiple line items for voucher transaction
  Future<JournalEntryModel?> _generateSingleVoucherJournalEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      final lines = <JournalEntryLineModel>[];
      double totalDebits = 0.0;
      double totalCredits = 0.0;

      // 1. NLC Amount (Total Freight) - DEBIT to Accounts Receivable
      if (voucher.totalFreight > 0 && voucher.companyFreightAccountId != null) {
        final nlcAccount = await _chartOfAccountsService
            .getAccountById(voucher.companyFreightAccountId!);
        if (nlcAccount != null) {
          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: nlcAccount.id,
            accountNumber: nlcAccount.accountNumber,
            accountName: nlcAccount.accountName,
            debitAmount: voucher.totalFreight,
            creditAmount: 0.0,
            description: 'NLC Amount - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalDebits += voucher.totalFreight;
        }
      }

      // 2. Broker Fees - CREDIT to Accounts Payable
      if (voucher.brokerFees > 0 && voucher.brokerAccountId != null) {
        final brokerAccount = await _chartOfAccountsService
            .getAccountById(voucher.brokerAccountId!);
        if (brokerAccount != null) {
          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: brokerAccount.id,
            accountNumber: brokerAccount.accountNumber,
            accountName: brokerAccount.accountName,
            debitAmount: 0.0,
            creditAmount: voucher.brokerFees,
            description: 'Broker Fees - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalCredits += voucher.brokerFees;
        }
      }

      // 3. Munshiana Fees - CREDIT to Service Revenue (Equity)
      if (voucher.munshianaFees > 0 && voucher.munshianaAccountId != null) {
        final munshianaAccount = await _chartOfAccountsService
            .getAccountById(voucher.munshianaAccountId!);
        if (munshianaAccount != null) {
          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: munshianaAccount.id,
            accountNumber: munshianaAccount.accountNumber,
            accountName: munshianaAccount.accountName,
            debitAmount: 0.0,
            creditAmount: voucher.munshianaFees,
            description: 'Munshiana Fees - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalCredits += voucher.munshianaFees;
        }
      }

      // 4. Truck/Wagon Freight - CREDIT to Accounts Payable
      final truckFreight =
          voucher.totalFreight - (voucher.brokerFees + voucher.munshianaFees);
      if (truckFreight > 0) {
        // Use truck freight account if available, otherwise use broker account
        final truckAccountId =
            voucher.truckFreightAccountId ?? voucher.brokerAccountId;
        if (truckAccountId != null) {
          final truckAccount =
              await _chartOfAccountsService.getAccountById(truckAccountId);
          if (truckAccount != null) {
            final line = JournalEntryLineModel(
              id: '',
              journalEntryId: '',
              accountId: truckAccount.id,
              accountNumber: truckAccount.accountNumber,
              accountName: truckAccount.accountName,
              debitAmount: 0.0,
              creditAmount: truckFreight,
              description:
                  'Truck/Wagon Freight - Voucher #${voucher.voucherNumber}',
              referenceId: voucher.voucherNumber,
              referenceType: 'voucher',
              createdAt: DateTime.now(),
            );
            lines.add(line);
            totalCredits += truckFreight;
          }
        }
      }

      // 5. Sales Tax (15%) - CREDIT to Tax Liability
      if (voucher.calculatedTax > 0 && voucher.salesTaxAccountId != null) {
        final salesTaxAccount = await _chartOfAccountsService
            .getAccountById(voucher.salesTaxAccountId!);
        if (salesTaxAccount != null) {
          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: salesTaxAccount.id,
            accountNumber: salesTaxAccount.accountNumber,
            accountName: salesTaxAccount.accountName,
            debitAmount: 0.0,
            creditAmount: voucher.calculatedTax,
            description: '15% Sales Tax - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalCredits += voucher.calculatedTax;
        }
      }

      // 6. Freight Tax (4.6%) - CREDIT to Tax Liability
      if (voucher.calculatedFreightTax > 0 &&
          voucher.freightTaxAccountId != null) {
        final freightTaxAccount = await _chartOfAccountsService
            .getAccountById(voucher.freightTaxAccountId!);
        if (freightTaxAccount != null) {
          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: freightTaxAccount.id,
            accountNumber: freightTaxAccount.accountNumber,
            accountName: freightTaxAccount.accountName,
            debitAmount: 0.0,
            creditAmount: voucher.calculatedFreightTax,
            description: '4.6% Freight Tax - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalCredits += voucher.calculatedFreightTax;
        }
      }

      // 7. Net Profit - CREDIT to Retained Earnings (Equity)
      if (voucher.calculatedProfit > 0 && voucher.profitAccountId != null) {
        final profitAccount = await _chartOfAccountsService
            .getAccountById(voucher.profitAccountId!);
        if (profitAccount != null) {
          final line = JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: profitAccount.id,
            accountNumber: profitAccount.accountNumber,
            accountName: profitAccount.accountName,
            debitAmount: 0.0,
            creditAmount: voucher.calculatedProfit,
            description: 'Net Profit - Voucher #${voucher.voucherNumber}',
            referenceId: voucher.voucherNumber,
            referenceType: 'voucher',
            createdAt: DateTime.now(),
          );
          lines.add(line);
          totalCredits += voucher.calculatedProfit;
        }
      }

      // Validate double-entry: debits must equal credits
      if (lines.isEmpty) {
        log('No valid account mappings found for voucher: ${voucher.voucherNumber}');
        return null;
      }

      if ((totalDebits - totalCredits).abs() > 0.01) {
        log('Double-entry validation failed for voucher ${voucher.voucherNumber}: Debits=$totalDebits, Credits=$totalCredits');
        return null;
      }

      // Create the journal entry
      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: DateTime.tryParse(voucher.departureDate) ?? DateTime.now(),
        description: 'Voucher Transaction - ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating single voucher journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from loan transaction
  Future<List<JournalEntryModel>> generateLoanJournalEntries({
    required LoanModel loan,
    required String transactionType, // 'disbursement' or 'repayment'
    required String uid,
    required String createdBy,
  }) async {
    final journalEntries = <JournalEntryModel>[];

    try {
      if (transactionType == 'disbursement') {
        // Loan disbursement entry
        final disbursementEntry = await _generateLoanDisbursementEntry(
          loan: loan,
          uid: uid,
          createdBy: createdBy,
        );
        if (disbursementEntry != null) journalEntries.add(disbursementEntry);
      } else if (transactionType == 'repayment') {
        // Loan repayment entry
        final repaymentEntry = await _generateLoanRepaymentEntry(
          loan: loan,
          uid: uid,
          createdBy: createdBy,
        );
        if (repaymentEntry != null) journalEntries.add(repaymentEntry);
      }

      return journalEntries;
    } catch (e) {
      log('Error generating loan journal entries: $e');
      return [];
    }
  }

  /// Generate journal entry from account transaction
  Future<JournalEntryModel?> generateAccountTransactionJournalEntry({
    required AccountTransactionModel transaction,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get the account
      final account = await _getAccountByName(transaction.accountName, uid);
      if (account == null) return null;

      // Determine the offsetting account based on transaction type
      ChartOfAccountsModel? offsetAccount;
      String description = transaction.description;

      switch (transaction.type) {
        case TransactionType.deposit:
          // Credit cash account, debit accounts receivable or revenue
          offsetAccount = await _getAccountByType(
            AccountType.currentAssets,
            'Accounts Receivable',
            uid,
          );
          break;
        case TransactionType.expense:
          // Debit expense account, credit cash account
          offsetAccount = await _getAccountByType(
            AccountType.operatingExpenses,
            'General Expenses',
            uid,
          );
          break;
        case TransactionType.loan:
          // Handle loan transactions
          if (transaction.amount > 0) {
            // Loan received - debit cash, credit loan payable
            offsetAccount = await _getAccountByType(
              AccountType.longTermLiabilities,
              'Loans Payable',
              uid,
            );
          } else {
            // Loan payment - debit loan payable, credit cash
            offsetAccount = await _getAccountByType(
              AccountType.longTermLiabilities,
              'Loans Payable',
              uid,
            );
          }
          break;
        default:
          // For other transaction types, use a general account
          offsetAccount = await _getAccountByType(
            AccountType.operatingExpenses,
            'Miscellaneous Expenses',
            uid,
          );
      }

      if (offsetAccount == null) return null;

      // Create journal entry lines based on transaction amount
      final lines = <JournalEntryLineModel>[];

      if (transaction.amount > 0) {
        // Positive amount - money coming in
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: account.id,
            accountName: account.accountName,
            accountNumber: account.accountNumber,
            description: description,
            debitAmount: transaction.amount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: offsetAccount.id,
            accountName: offsetAccount.accountName,
            accountNumber: offsetAccount.accountNumber,
            description: description,
            debitAmount: 0.0,
            creditAmount: transaction.amount,
            createdAt: DateTime.now(),
          ),
        ]);
      } else {
        // Negative amount - money going out
        final positiveAmount = transaction.amount.abs();
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: offsetAccount.id,
            accountName: offsetAccount.accountName,
            accountNumber: offsetAccount.accountNumber,
            description: description,
            debitAmount: positiveAmount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: account.id,
            accountName: account.accountName,
            accountNumber: account.accountNumber,
            description: description,
            debitAmount: 0.0,
            creditAmount: positiveAmount,
            createdAt: DateTime.now(),
          ),
        ]);
      }

      final totalAmount = transaction.amount.abs();
      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: transaction.transactionDate,
        description: description,
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalAmount,
        totalCredits: totalAmount,
        referenceNumber: transaction.referenceId,
        sourceTransactionId: transaction.id,
        sourceTransactionType: 'account_transaction',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating account transaction journal entry: $e');
      return null;
    }
  }

  // Helper methods
  Future<ChartOfAccountsModel?> _getAccountByName(
      String accountName, String uid) async {
    try {
      final accounts = await _chartOfAccountsService.getAccounts();
      return accounts.firstWhere(
        (account) =>
            account.accountName.toLowerCase() == accountName.toLowerCase(),
        orElse: () => accounts.first, // Fallback to first account
      );
    } catch (e) {
      return null;
    }
  }

  Future<ChartOfAccountsModel?> _getAccountByType(
    AccountType accountType,
    String preferredName,
    String uid,
  ) async {
    try {
      final accounts =
          await _chartOfAccountsService.getAccountsByType(accountType);
      // First try to find by preferred name and type
      try {
        return accounts.firstWhere(
          (account) => account.accountName
              .toLowerCase()
              .contains(preferredName.toLowerCase()),
        );
      } catch (e) {
        // If not found, get any account of the specified type
        try {
          return accounts.first;
        } catch (e) {
          return null;
        }
      }
    } catch (e) {
      return null;
    }
  }

  // Loan-specific helper methods
  Future<JournalEntryModel?> _generateLoanDisbursementEntry({
    required LoanModel loan,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get cash account (money going out)
      final cashAccount = await _getAccountByName(loan.fromAccountName, uid);
      if (cashAccount == null) return null;

      // Get loan receivable account (money owed to us)
      final loanReceivableAccount = await _getAccountByType(
        AccountType.currentAssets,
        'Loans Receivable',
        uid,
      );
      if (loanReceivableAccount == null) return null;

      final lines = <JournalEntryLineModel>[
        // Debit loan receivable
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: loanReceivableAccount.id,
          accountName: loanReceivableAccount.accountName,
          accountNumber: loanReceivableAccount.accountNumber,
          description: 'Loan disbursed to ${loan.requestedByName}',
          debitAmount: loan.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit cash account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Loan payment to ${loan.requestedByName}',
          debitAmount: 0.0,
          creditAmount: loan.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: loan.approvalDate ?? DateTime.now(),
        description: 'Loan disbursement to ${loan.requestedByName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: loan.amount,
        totalCredits: loan.amount,
        referenceNumber: loan.id,
        sourceTransactionId: loan.id,
        sourceTransactionType: 'loan_disbursement',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating loan disbursement entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateLoanRepaymentEntry({
    required LoanModel loan,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get cash account (money coming in)
      final cashAccount = await _getAccountByName(loan.toAccountName, uid);
      if (cashAccount == null) return null;

      // Get loan receivable account (reducing what's owed to us)
      final loanReceivableAccount = await _getAccountByType(
        AccountType.currentAssets,
        'Loans Receivable',
        uid,
      );
      if (loanReceivableAccount == null) return null;

      final lines = <JournalEntryLineModel>[
        // Debit cash account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Loan repayment from ${loan.requestedByName}',
          debitAmount: loan.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit loan receivable
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: loanReceivableAccount.id,
          accountName: loanReceivableAccount.accountName,
          accountNumber: loanReceivableAccount.accountNumber,
          description: 'Loan repayment from ${loan.requestedByName}',
          debitAmount: 0.0,
          creditAmount: loan.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: loan.repaymentDate ?? DateTime.now(),
        description: 'Loan repayment from ${loan.requestedByName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: loan.amount,
        totalCredits: loan.amount,
        referenceNumber: loan.id,
        sourceTransactionId: loan.id,
        sourceTransactionType: 'loan_repayment',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating loan repayment entry: $e');
      return null;
    }
  }
}
