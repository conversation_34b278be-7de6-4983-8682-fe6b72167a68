import 'dart:developer';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/expense_model.dart';
import '../../models/finance/bill_model.dart';
import '../../models/voucher_model.dart';
import '../../models/finance/loan_model.dart';
import '../../models/finance/account_transaction_model.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';

/// Service for automatically generating journal entries from existing transactions
class AutomaticJournalEntryService {
  final ChartOfAccountsFirebaseService _chartOfAccountsService;

  AutomaticJournalEntryService(
    this._chartOfAccountsService,
  );

  /// Determine if an account should be debited or credited based on account type and transaction nature
  /// Returns true for debit, false for credit
  bool _shouldDebitAccount({
    required AccountCategory accountCategory,
    required bool isMoneyIncoming,
  }) {
    switch (accountCategory) {
      case AccountCategory.assets:
        // Assets increase with debits (incoming money), decrease with credits (outgoing money)
        return isMoneyIncoming;
      case AccountCategory.expenses:
        // Expenses increase with debits (money spent), rarely decrease with credits
        return !isMoneyIncoming; // Expense when money goes out
      case AccountCategory.revenue:
        // Revenue increases with credits (money earned), rarely decreases with debits
        return !isMoneyIncoming; // Revenue when money comes in (credit)
      case AccountCategory.liabilities:
        // Liabilities increase with credits (new debt), decrease with debits (payment made)
        return !isMoneyIncoming; // Credit when liability created, debit when paid
      case AccountCategory.equity:
        // Equity increases with credits (profit/investment), decreases with debits (loss/withdrawal)
        return !isMoneyIncoming; // Credit when equity increases
    }
  }

  /// Generate journal entry from expense transaction
  Future<JournalEntryModel?> generateExpenseJournalEntry({
    required ExpenseModel expense,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get expense account (should be an expense account)
      final expenseAccount = await _getAccountByName(expense.categoryName, uid);
      if (expenseAccount == null) return null;

      // Get cash/bank account (should be an asset account)
      final cashAccount = await _getAccountByName(expense.accountName, uid);
      if (cashAccount == null) return null;

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[
        // Debit expense account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: expenseAccount.id,
          accountName: expenseAccount.accountName,
          accountNumber: expenseAccount.accountNumber,
          description: 'Expense: ${expense.title}',
          debitAmount: expense.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit cash/bank account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Payment for: ${expense.title}',
          debitAmount: 0.0,
          creditAmount: expense.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: expense.createdAt,
        description: 'Expense: ${expense.title} - ${expense.payeeName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: expense.amount,
        totalCredits: expense.amount,
        referenceNumber: expense.referenceNumber,
        sourceTransactionId: expense.id,
        sourceTransactionType: 'expense',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating expense journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from bill transaction
  Future<JournalEntryModel?> generateBillJournalEntry({
    required BillModel bill,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get accounts payable account
      final accountsPayableAccount = await _getAccountByType(
        AccountType.currentLiabilities,
        'Accounts Payable',
        uid,
      );
      if (accountsPayableAccount == null) return null;

      // Get revenue/service account
      final revenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Service Revenue',
        uid,
      );
      if (revenueAccount == null) return null;

      // Create journal entry lines
      final lines = <JournalEntryLineModel>[
        // Debit accounts receivable (if bill represents revenue)
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: accountsPayableAccount.id,
          accountName: accountsPayableAccount.accountName,
          accountNumber: accountsPayableAccount.accountNumber,
          description: 'Bill: ${bill.billNumber}',
          debitAmount: bill.totalAmount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit revenue account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: revenueAccount.id,
          accountName: revenueAccount.accountName,
          accountNumber: revenueAccount.accountNumber,
          description: 'Revenue from bill: ${bill.billNumber}',
          debitAmount: 0.0,
          creditAmount: bill.totalAmount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: bill.billDate,
        description:
            'Bill: ${bill.billNumber} - ${bill.customerName ?? "Multiple Customers"}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: bill.totalAmount,
        totalCredits: bill.totalAmount,
        referenceNumber: bill.billNumber,
        sourceTransactionId: bill.billId,
        sourceTransactionType: 'bill',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating bill journal entry: $e');
      return null;
    }
  }

  /// Generate journal entry from voucher transaction
  Future<List<JournalEntryModel>> generateVoucherJournalEntries({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    final journalEntries = <JournalEntryModel>[];

    try {
      // 1. NLC Amount (Total Freight) journal entry
      if (voucher.totalFreight > 0) {
        final nlcEntry = await _generateNLCAmountEntry(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );
        if (nlcEntry != null) journalEntries.add(nlcEntry);
      }

      // 2. Broker fees journal entry
      if (voucher.brokerFees > 0) {
        final brokerEntry = await _generateBrokerFeesEntry(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );
        if (brokerEntry != null) journalEntries.add(brokerEntry);
      }

      // 3. Munshiana fees journal entry
      if (voucher.munshianaFees > 0) {
        final munshianaEntry = await _generateMunshianaEntry(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );
        if (munshianaEntry != null) journalEntries.add(munshianaEntry);
      }

      // 4. Truck freight journal entry
      final truckFreight =
          voucher.totalFreight - (voucher.brokerFees + voucher.munshianaFees);
      if (truckFreight > 0) {
        final truckFreightEntry = await _generateTruckFreightEntry(
          voucher: voucher,
          truckFreightAmount: truckFreight,
          uid: uid,
          createdBy: createdBy,
        );
        if (truckFreightEntry != null) journalEntries.add(truckFreightEntry);
      }

      // 5. Sales tax journal entry (15% tax)
      if (voucher.calculatedTax > 0) {
        final salesTaxEntry = await _generateSalesTaxEntry(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );
        if (salesTaxEntry != null) journalEntries.add(salesTaxEntry);
      }

      // 6. Freight tax journal entry (4.6% tax)
      if (voucher.calculatedFreightTax > 0) {
        final freightTaxEntry = await _generateFreightTaxEntry(
          voucher: voucher,
          uid: uid,
          createdBy: createdBy,
        );
        if (freightTaxEntry != null) journalEntries.add(freightTaxEntry);
      }

      // Note: Net profit is automatically calculated from the above entries
      // No separate net profit entry needed as individual components are already recorded

      return journalEntries;
    } catch (e) {
      log('Error generating voucher journal entries: $e');
      return [];
    }
  }

  /// Generate journal entry from loan transaction
  Future<List<JournalEntryModel>> generateLoanJournalEntries({
    required LoanModel loan,
    required String transactionType, // 'disbursement' or 'repayment'
    required String uid,
    required String createdBy,
  }) async {
    final journalEntries = <JournalEntryModel>[];

    try {
      if (transactionType == 'disbursement') {
        // Loan disbursement entry
        final disbursementEntry = await _generateLoanDisbursementEntry(
          loan: loan,
          uid: uid,
          createdBy: createdBy,
        );
        if (disbursementEntry != null) journalEntries.add(disbursementEntry);
      } else if (transactionType == 'repayment') {
        // Loan repayment entry
        final repaymentEntry = await _generateLoanRepaymentEntry(
          loan: loan,
          uid: uid,
          createdBy: createdBy,
        );
        if (repaymentEntry != null) journalEntries.add(repaymentEntry);
      }

      return journalEntries;
    } catch (e) {
      log('Error generating loan journal entries: $e');
      return [];
    }
  }

  /// Generate journal entry from account transaction
  Future<JournalEntryModel?> generateAccountTransactionJournalEntry({
    required AccountTransactionModel transaction,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get the account
      final account = await _getAccountByName(transaction.accountName, uid);
      if (account == null) return null;

      // Determine the offsetting account based on transaction type
      ChartOfAccountsModel? offsetAccount;
      String description = transaction.description;

      switch (transaction.type) {
        case TransactionType.deposit:
          // Credit cash account, debit accounts receivable or revenue
          offsetAccount = await _getAccountByType(
            AccountType.currentAssets,
            'Accounts Receivable',
            uid,
          );
          break;
        case TransactionType.expense:
          // Debit expense account, credit cash account
          offsetAccount = await _getAccountByType(
            AccountType.operatingExpenses,
            'General Expenses',
            uid,
          );
          break;
        case TransactionType.loan:
          // Handle loan transactions
          if (transaction.amount > 0) {
            // Loan received - debit cash, credit loan payable
            offsetAccount = await _getAccountByType(
              AccountType.longTermLiabilities,
              'Loans Payable',
              uid,
            );
          } else {
            // Loan payment - debit loan payable, credit cash
            offsetAccount = await _getAccountByType(
              AccountType.longTermLiabilities,
              'Loans Payable',
              uid,
            );
          }
          break;
        default:
          // For other transaction types, use a general account
          offsetAccount = await _getAccountByType(
            AccountType.operatingExpenses,
            'Miscellaneous Expenses',
            uid,
          );
      }

      if (offsetAccount == null) return null;

      // Create journal entry lines based on transaction amount
      final lines = <JournalEntryLineModel>[];

      if (transaction.amount > 0) {
        // Positive amount - money coming in
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: account.id,
            accountName: account.accountName,
            accountNumber: account.accountNumber,
            description: description,
            debitAmount: transaction.amount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: offsetAccount.id,
            accountName: offsetAccount.accountName,
            accountNumber: offsetAccount.accountNumber,
            description: description,
            debitAmount: 0.0,
            creditAmount: transaction.amount,
            createdAt: DateTime.now(),
          ),
        ]);
      } else {
        // Negative amount - money going out
        final positiveAmount = transaction.amount.abs();
        lines.addAll([
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: offsetAccount.id,
            accountName: offsetAccount.accountName,
            accountNumber: offsetAccount.accountNumber,
            description: description,
            debitAmount: positiveAmount,
            creditAmount: 0.0,
            createdAt: DateTime.now(),
          ),
          JournalEntryLineModel(
            id: '',
            journalEntryId: '',
            accountId: account.id,
            accountName: account.accountName,
            accountNumber: account.accountNumber,
            description: description,
            debitAmount: 0.0,
            creditAmount: positiveAmount,
            createdAt: DateTime.now(),
          ),
        ]);
      }

      final totalAmount = transaction.amount.abs();
      return JournalEntryModel(
        id: '',
        entryNumber: '', // Will be auto-generated
        entryDate: transaction.transactionDate,
        description: description,
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: totalAmount,
        totalCredits: totalAmount,
        referenceNumber: transaction.referenceId,
        sourceTransactionId: transaction.id,
        sourceTransactionType: 'account_transaction',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating account transaction journal entry: $e');
      return null;
    }
  }

  // Helper methods
  Future<ChartOfAccountsModel?> _getAccountByName(
      String accountName, String uid) async {
    try {
      final accounts = await _chartOfAccountsService.getAccounts();
      return accounts.firstWhere(
        (account) =>
            account.accountName.toLowerCase() == accountName.toLowerCase(),
        orElse: () => accounts.first, // Fallback to first account
      );
    } catch (e) {
      return null;
    }
  }

  Future<ChartOfAccountsModel?> _getAccountById(
      String accountId, String uid) async {
    try {
      final accounts = await _chartOfAccountsService.getAccounts();
      return accounts.firstWhere(
        (account) => account.id == accountId && account.uid == uid,
      );
    } catch (e) {
      return null;
    }
  }

  Future<ChartOfAccountsModel?> _getAccountByType(
    AccountType accountType,
    String preferredName,
    String uid,
  ) async {
    try {
      final accounts =
          await _chartOfAccountsService.getAccountsByType(accountType);
      // First try to find by preferred name and type
      try {
        return accounts.firstWhere(
          (account) => account.accountName
              .toLowerCase()
              .contains(preferredName.toLowerCase()),
        );
      } catch (e) {
        // If not found, get any account of the specified type
        try {
          return accounts.first;
        } catch (e) {
          return null;
        }
      }
    } catch (e) {
      return null;
    }
  }

  // Voucher-specific helper methods
  Future<JournalEntryModel?> _generateNLCAmountEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get accounts receivable account for NLC amount (Asset account) - Updated for Chart of Accounts
      ChartOfAccountsModel? receivableAccount;
      if (voucher.companyFreightAccountId != null &&
          voucher.companyFreightAccountId!.isNotEmpty) {
        // Use Chart of Accounts ID if available
        receivableAccount =
            await _getAccountById(voucher.companyFreightAccountId!, uid);
      }

      if (receivableAccount == null) {
        // Fallback to default accounts receivable account
        receivableAccount = await _getAccountByType(
          AccountType.accountsReceivable,
          'Accounts Receivable',
          uid,
        );
      }
      if (receivableAccount == null) return null;

      // Get revenue account for freight services (Revenue account)
      final revenueAccount = await _getAccountByType(
        AccountType.serviceRevenue,
        'Freight Revenue',
        uid,
      );
      if (revenueAccount == null) return null;

      // Determine debit/credit based on account types and transaction nature
      // NLC Amount represents money incoming from clients
      final shouldDebitReceivable = _shouldDebitAccount(
        accountCategory: receivableAccount.category,
        isMoneyIncoming: true, // Money coming from clients
      );
      final shouldDebitRevenue = _shouldDebitAccount(
        accountCategory: revenueAccount.category,
        isMoneyIncoming: true, // Revenue earned
      );

      final lines = <JournalEntryLineModel>[
        // Accounts Receivable entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: receivableAccount.id,
          accountName: receivableAccount.accountName,
          accountNumber: receivableAccount.accountNumber,
          description: 'NLC Amount - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitReceivable ? voucher.totalFreight : 0.0,
          creditAmount: shouldDebitReceivable ? 0.0 : voucher.totalFreight,
          createdAt: DateTime.now(),
        ),
        // Freight Revenue entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: revenueAccount.id,
          accountName: revenueAccount.accountName,
          accountNumber: revenueAccount.accountNumber,
          description: 'Freight revenue - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitRevenue ? voucher.totalFreight : 0.0,
          creditAmount: shouldDebitRevenue ? 0.0 : voucher.totalFreight,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description: 'NLC Amount entry - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: voucher.totalFreight,
        totalCredits: voucher.totalFreight,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_nlc_amount',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating NLC amount entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateBrokerFeesEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get broker fees expense account - Updated for Chart of Accounts
      ChartOfAccountsModel? brokerFeesAccount;
      if (voucher.brokerAccountId != null &&
          voucher.brokerAccountId!.isNotEmpty) {
        // Use Chart of Accounts ID if available
        brokerFeesAccount =
            await _getAccountById(voucher.brokerAccountId!, uid);
      } else {
        // Fallback to legacy account name lookup
        brokerFeesAccount = await _getAccountByName(voucher.brokerAccount, uid);
      }

      if (brokerFeesAccount == null) {
        // Final fallback to default broker fees account
        brokerFeesAccount = await _getAccountByType(
          AccountType.operatingExpenses,
          'Broker Fees',
          uid,
        );
      }
      if (brokerFeesAccount == null) return null;

      // Get cash/bank account for broker payment - Updated for Chart of Accounts
      ChartOfAccountsModel? brokerCashAccount;
      if (voucher.brokerAccountId != null &&
          voucher.brokerAccountId!.isNotEmpty) {
        // For Chart of Accounts, we need a cash/bank account for the payment
        // This should be a separate field or use a default cash account
        brokerCashAccount = await _getAccountByType(
          AccountType.cash,
          'Cash',
          uid,
        );
      } else {
        // Legacy fallback
        brokerCashAccount = await _getAccountByName(voucher.brokerAccount, uid);
      }
      if (brokerCashAccount == null) return null;

      // Determine debit/credit based on account types and transaction nature
      // Broker fees represent a liability created (money owed to broker)
      final shouldDebitBrokerAccount = _shouldDebitAccount(
        accountCategory: brokerFeesAccount.category,
        isMoneyIncoming: false, // Liability created, not money incoming
      );
      // Cash payment represents money going out
      final shouldDebitCashAccount = _shouldDebitAccount(
        accountCategory: brokerCashAccount.category,
        isMoneyIncoming: false, // Money going out for payment
      );

      final lines = <JournalEntryLineModel>[
        // Broker fees account entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: brokerFeesAccount.id,
          accountName: brokerFeesAccount.accountName,
          accountNumber: brokerFeesAccount.accountNumber,
          description: 'Broker fees - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitBrokerAccount ? voucher.brokerFees : 0.0,
          creditAmount: shouldDebitBrokerAccount ? 0.0 : voucher.brokerFees,
          createdAt: DateTime.now(),
        ),
        // Cash account entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: brokerCashAccount.id,
          accountName: brokerCashAccount.accountName,
          accountNumber: brokerCashAccount.accountNumber,
          description: 'Broker payment - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitCashAccount ? voucher.brokerFees : 0.0,
          creditAmount: shouldDebitCashAccount ? 0.0 : voucher.brokerFees,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description: 'Broker fees payment - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: voucher.brokerFees,
        totalCredits: voucher.brokerFees,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_broker_fees',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating broker fees entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateMunshianaEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get munshiana expense account - Updated for Chart of Accounts
      ChartOfAccountsModel? munshianaAccount;
      if (voucher.munshianaAccountId != null &&
          voucher.munshianaAccountId!.isNotEmpty) {
        // Use Chart of Accounts ID if available
        munshianaAccount =
            await _getAccountById(voucher.munshianaAccountId!, uid);
      } else {
        // Fallback to legacy account name lookup
        munshianaAccount =
            await _getAccountByName(voucher.munshianaAccount, uid);
      }

      if (munshianaAccount == null) {
        // Final fallback to default munshiana account
        munshianaAccount = await _getAccountByType(
          AccountType.equityServiceRevenue,
          'Munshiana',
          uid,
        );
      }
      if (munshianaAccount == null) return null;

      // Get cash/bank account for munshiana payment - Updated for Chart of Accounts
      ChartOfAccountsModel? munshianaCashAccount;
      if (voucher.munshianaAccountId != null &&
          voucher.munshianaAccountId!.isNotEmpty) {
        // For Chart of Accounts, use a default cash account for the payment
        munshianaCashAccount = await _getAccountByType(
          AccountType.cash,
          'Cash',
          uid,
        );
      } else {
        // Legacy fallback
        munshianaCashAccount =
            await _getAccountByName(voucher.munshianaAccount, uid);
      }
      if (munshianaCashAccount == null) return null;

      // Determine debit/credit based on account types and transaction nature
      // Munshiana fees represent equity/service revenue earned (money incoming to equity)
      final shouldDebitMunshianaAccount = _shouldDebitAccount(
        accountCategory: munshianaAccount.category,
        isMoneyIncoming: true, // Equity/service revenue earned
      );
      // Cash payment represents money going out
      final shouldDebitCashAccount = _shouldDebitAccount(
        accountCategory: munshianaCashAccount.category,
        isMoneyIncoming: false, // Money going out for payment
      );

      final lines = <JournalEntryLineModel>[
        // Munshiana fees account entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: munshianaAccount.id,
          accountName: munshianaAccount.accountName,
          accountNumber: munshianaAccount.accountNumber,
          description: 'Munshiana fees - Voucher ${voucher.voucherNumber}',
          debitAmount:
              shouldDebitMunshianaAccount ? voucher.munshianaFees : 0.0,
          creditAmount:
              shouldDebitMunshianaAccount ? 0.0 : voucher.munshianaFees,
          createdAt: DateTime.now(),
        ),
        // Cash account entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: munshianaCashAccount.id,
          accountName: munshianaCashAccount.accountName,
          accountNumber: munshianaCashAccount.accountNumber,
          description: 'Munshiana payment - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitCashAccount ? voucher.munshianaFees : 0.0,
          creditAmount: shouldDebitCashAccount ? 0.0 : voucher.munshianaFees,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description: 'Munshiana payment - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: voucher.munshianaFees,
        totalCredits: voucher.munshianaFees,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_munshiana',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating munshiana entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateTruckFreightEntry({
    required VoucherModel voucher,
    required double truckFreightAmount,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get truck freight liability account - Updated for Chart of Accounts
      ChartOfAccountsModel? truckFreightAccount;
      if (voucher.truckFreightAccountId != null &&
          voucher.truckFreightAccountId!.isNotEmpty) {
        // Use Chart of Accounts ID if available
        truckFreightAccount =
            await _getAccountById(voucher.truckFreightAccountId!, uid);
      }

      if (truckFreightAccount == null) {
        // Fallback to default truck freight account
        truckFreightAccount = await _getAccountByType(
          AccountType.accountsPayable,
          'Truck Freight Payable',
          uid,
        );
      }
      if (truckFreightAccount == null) return null;

      // Get cash/bank account for truck freight payment - Updated for Chart of Accounts
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
        uid,
      );
      if (cashAccount == null) return null;

      // Determine debit/credit based on account types and transaction nature
      // Truck freight represents money owed to truck owners (liability)
      final shouldDebitTruckFreight = _shouldDebitAccount(
        accountCategory: truckFreightAccount.category,
        isMoneyIncoming: false, // Creating liability (money owed)
      );
      final shouldDebitCash = _shouldDebitAccount(
        accountCategory: cashAccount.category,
        isMoneyIncoming: false, // Money going out for payment
      );

      final lines = <JournalEntryLineModel>[
        // Truck freight liability entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: truckFreightAccount.id,
          accountName: truckFreightAccount.accountName,
          accountNumber: truckFreightAccount.accountNumber,
          description:
              'Truck freight expense - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitTruckFreight ? truckFreightAmount : 0.0,
          creditAmount: shouldDebitTruckFreight ? 0.0 : truckFreightAmount,
          createdAt: DateTime.now(),
        ),
        // Cash payment entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description:
              'Truck freight payment - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitCash ? truckFreightAmount : 0.0,
          creditAmount: shouldDebitCash ? 0.0 : truckFreightAmount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description: 'Truck freight payment - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: truckFreightAmount,
        totalCredits: truckFreightAmount,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_truck_freight',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating truck freight entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateSalesTaxEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get sales tax account - Updated for Chart of Accounts
      ChartOfAccountsModel? salesTaxAccount;
      if (voucher.salesTaxAccountId != null &&
          voucher.salesTaxAccountId!.isNotEmpty) {
        // Use Chart of Accounts ID if available
        salesTaxAccount =
            await _getAccountById(voucher.salesTaxAccountId!, uid);
      }

      if (salesTaxAccount == null) {
        // Fallback to default sales tax account
        salesTaxAccount = await _getAccountByType(
          AccountType.currentLiabilities,
          'Sales Tax Payable',
          uid,
        );
      }
      if (salesTaxAccount == null) return null;

      // Get cash account for tax payment
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
        uid,
      );
      if (cashAccount == null) return null;

      // Determine debit/credit based on account types and transaction nature
      // Sales tax represents a liability created (money owed to tax authority)
      final shouldDebitSalesTaxAccount = _shouldDebitAccount(
        accountCategory: salesTaxAccount.category,
        isMoneyIncoming: false, // Liability created, not money incoming
      );
      // Cash payment represents money going out
      final shouldDebitCashAccount = _shouldDebitAccount(
        accountCategory: cashAccount.category,
        isMoneyIncoming: false, // Money going out for payment
      );

      final lines = <JournalEntryLineModel>[
        // Sales tax account entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: salesTaxAccount.id,
          accountName: salesTaxAccount.accountName,
          accountNumber: salesTaxAccount.accountNumber,
          description: 'Sales tax (15%) - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitSalesTaxAccount ? voucher.calculatedTax : 0.0,
          creditAmount:
              shouldDebitSalesTaxAccount ? 0.0 : voucher.calculatedTax,
          createdAt: DateTime.now(),
        ),
        // Cash account entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Sales tax payment - Voucher ${voucher.voucherNumber}',
          debitAmount: shouldDebitCashAccount ? voucher.calculatedTax : 0.0,
          creditAmount: shouldDebitCashAccount ? 0.0 : voucher.calculatedTax,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description: 'Sales tax payment - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: voucher.calculatedTax,
        totalCredits: voucher.calculatedTax,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_sales_tax',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating sales tax entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateFreightTaxEntry({
    required VoucherModel voucher,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get freight tax account - Updated for Chart of Accounts
      ChartOfAccountsModel? freightTaxAccount;
      if (voucher.freightTaxAccountId != null &&
          voucher.freightTaxAccountId!.isNotEmpty) {
        // Use Chart of Accounts ID if available
        freightTaxAccount =
            await _getAccountById(voucher.freightTaxAccountId!, uid);
      }

      if (freightTaxAccount == null) {
        // Fallback to default freight tax account
        freightTaxAccount = await _getAccountByType(
          AccountType.currentLiabilities,
          'Freight Tax Payable',
          uid,
        );
      }
      if (freightTaxAccount == null) return null;

      // Get cash account for tax payment
      final cashAccount = await _getAccountByType(
        AccountType.cash,
        'Cash',
        uid,
      );
      if (cashAccount == null) return null;

      // Determine debit/credit based on account types and transaction nature
      // Freight tax represents a liability created (money owed to tax authority)
      final shouldDebitFreightTaxAccount = _shouldDebitAccount(
        accountCategory: freightTaxAccount.category,
        isMoneyIncoming: false, // Liability created, not money incoming
      );
      // Cash payment represents money going out
      final shouldDebitCashAccount = _shouldDebitAccount(
        accountCategory: cashAccount.category,
        isMoneyIncoming: false, // Money going out for payment
      );

      final lines = <JournalEntryLineModel>[
        // Freight tax account entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: freightTaxAccount.id,
          accountName: freightTaxAccount.accountName,
          accountNumber: freightTaxAccount.accountNumber,
          description: 'Freight tax (4.6%) - Voucher ${voucher.voucherNumber}',
          debitAmount:
              shouldDebitFreightTaxAccount ? voucher.calculatedFreightTax : 0.0,
          creditAmount:
              shouldDebitFreightTaxAccount ? 0.0 : voucher.calculatedFreightTax,
          createdAt: DateTime.now(),
        ),
        // Cash account entry
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Freight tax payment - Voucher ${voucher.voucherNumber}',
          debitAmount:
              shouldDebitCashAccount ? voucher.calculatedFreightTax : 0.0,
          creditAmount:
              shouldDebitCashAccount ? 0.0 : voucher.calculatedFreightTax,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: voucher.belongsToDate ?? voucher.createdAt,
        description: 'Freight tax payment - Voucher ${voucher.voucherNumber}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: voucher.calculatedFreightTax,
        totalCredits: voucher.calculatedFreightTax,
        referenceNumber: voucher.voucherNumber,
        sourceTransactionId: voucher.voucherNumber,
        sourceTransactionType: 'voucher_freight_tax',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating freight tax entry: $e');
      return null;
    }
  }

  // Loan-specific helper methods
  Future<JournalEntryModel?> _generateLoanDisbursementEntry({
    required LoanModel loan,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get cash account (money going out)
      final cashAccount = await _getAccountByName(loan.fromAccountName, uid);
      if (cashAccount == null) return null;

      // Get loan receivable account (money owed to us)
      final loanReceivableAccount = await _getAccountByType(
        AccountType.currentAssets,
        'Loans Receivable',
        uid,
      );
      if (loanReceivableAccount == null) return null;

      final lines = <JournalEntryLineModel>[
        // Debit loan receivable
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: loanReceivableAccount.id,
          accountName: loanReceivableAccount.accountName,
          accountNumber: loanReceivableAccount.accountNumber,
          description: 'Loan disbursed to ${loan.requestedByName}',
          debitAmount: loan.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit cash account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Loan payment to ${loan.requestedByName}',
          debitAmount: 0.0,
          creditAmount: loan.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: loan.approvalDate ?? DateTime.now(),
        description: 'Loan disbursement to ${loan.requestedByName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: loan.amount,
        totalCredits: loan.amount,
        referenceNumber: loan.id,
        sourceTransactionId: loan.id,
        sourceTransactionType: 'loan_disbursement',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating loan disbursement entry: $e');
      return null;
    }
  }

  Future<JournalEntryModel?> _generateLoanRepaymentEntry({
    required LoanModel loan,
    required String uid,
    required String createdBy,
  }) async {
    try {
      // Get cash account (money coming in)
      final cashAccount = await _getAccountByName(loan.toAccountName, uid);
      if (cashAccount == null) return null;

      // Get loan receivable account (reducing what's owed to us)
      final loanReceivableAccount = await _getAccountByType(
        AccountType.currentAssets,
        'Loans Receivable',
        uid,
      );
      if (loanReceivableAccount == null) return null;

      final lines = <JournalEntryLineModel>[
        // Debit cash account
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: cashAccount.id,
          accountName: cashAccount.accountName,
          accountNumber: cashAccount.accountNumber,
          description: 'Loan repayment from ${loan.requestedByName}',
          debitAmount: loan.amount,
          creditAmount: 0.0,
          createdAt: DateTime.now(),
        ),
        // Credit loan receivable
        JournalEntryLineModel(
          id: '',
          journalEntryId: '',
          accountId: loanReceivableAccount.id,
          accountName: loanReceivableAccount.accountName,
          accountNumber: loanReceivableAccount.accountNumber,
          description: 'Loan repayment from ${loan.requestedByName}',
          debitAmount: 0.0,
          creditAmount: loan.amount,
          createdAt: DateTime.now(),
        ),
      ];

      return JournalEntryModel(
        id: '',
        entryNumber: '',
        entryDate: loan.repaymentDate ?? DateTime.now(),
        description: 'Loan repayment from ${loan.requestedByName}',
        entryType: JournalEntryType.automatic,
        status: JournalEntryStatus.posted,
        lines: lines,
        totalDebits: loan.amount,
        totalCredits: loan.amount,
        referenceNumber: loan.id,
        sourceTransactionId: loan.id,
        sourceTransactionType: 'loan_repayment',
        createdAt: DateTime.now(),
        createdBy: createdBy,
        uid: uid,
      );
    } catch (e) {
      log('Error generating loan repayment entry: $e');
      return null;
    }
  }
}
