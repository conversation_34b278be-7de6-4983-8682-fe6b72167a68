import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../models/finance/journal_entry_model.dart';
import '../controllers/journal_entry_controller.dart';
import '../widgets/journal_entry_card.dart';
import '../widgets/create_journal_entry_dialog.dart';

class JournalEntriesScreen extends StatelessWidget {
  const JournalEntriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    log('🔍 [UI DEBUG] JournalEntriesScreen build() called');
    final controller = Get.find<JournalEntryController>();
    log('🔍 [UI DEBUG] Controller found: ${controller.runtimeType}');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Journal Entries'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Debug test button
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              log('🔍 [UI DEBUG] Test button pressed');
              controller.testDebugLogs();
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadJournalEntries(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Cards
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    context,
                    'Total Entries',
                    controller.journalEntries.length.toString(),
                    Icons.receipt_long,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(() {
                    final draftCount = controller.journalEntries
                        .where(
                            (entry) => entry.status == JournalEntryStatus.draft)
                        .length;
                    return _buildSummaryCard(
                      context,
                      'Draft Entries',
                      draftCount.toString(),
                      Icons.edit_note,
                      Colors.orange,
                    );
                  }),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(() {
                    final postedCount = controller.journalEntries
                        .where((entry) =>
                            entry.status == JournalEntryStatus.posted)
                        .length;
                    return _buildSummaryCard(
                      context,
                      'Posted Entries',
                      postedCount.toString(),
                      Icons.check_circle,
                      Colors.green,
                    );
                  }),
                ),
              ],
            ),
          ),

          // Journal Entries List
          Expanded(
            child: Obx(() {
              // Debug logging for UI state
              log('🔍 [UI DEBUG] Obx rebuilding - isLoading: ${controller.isLoading.value}');
              log('🔍 [UI DEBUG] Obx rebuilding - entries count: ${controller.journalEntries.length}');

              if (controller.isLoading.value) {
                log('🔍 [UI DEBUG] Showing loading indicator');
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (controller.journalEntries.isEmpty) {
                log('🔍 [UI DEBUG] Showing empty state');
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No journal entries found',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create your first journal entry to get started',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[500],
                            ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () => _showCreateJournalEntryDialog(context),
                        icon: const Icon(Icons.add),
                        label: const Text('Create Journal Entry'),
                      ),
                    ],
                  ),
                );
              }

              log('🔍 [UI DEBUG] Showing list view with ${controller.journalEntries.length} entries');
              return RefreshIndicator(
                onRefresh: controller.loadJournalEntries,
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: controller.journalEntries.length,
                  itemBuilder: (context, index) {
                    final entry = controller.journalEntries[index];
                    log('🔍 [UI DEBUG] Building item $index: ${entry.entryNumber}');
                    return JournalEntryCard(
                      journalEntry: entry,
                      onTap: () => _showJournalEntryDetails(context, entry),
                      onPost: entry.status == JournalEntryStatus.draft
                          ? () => _confirmPostEntry(context, entry)
                          : null,
                      onDelete: entry.status == JournalEntryStatus.draft
                          ? () => _confirmDeleteEntry(context, entry)
                          : null,
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateJournalEntryDialog(context),
        icon: const Icon(Icons.add),
        label: const Text('New Entry'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateJournalEntryDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CreateJournalEntryDialog(),
    );
  }

  void _showJournalEntryDetails(BuildContext context, JournalEntryModel entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Journal Entry ${entry.entryNumber}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Date', entry.entryDate.toString().split(' ')[0]),
              _buildDetailRow('Description', entry.description),
              _buildDetailRow('Status', entry.status.displayName),
              _buildDetailRow('Type', entry.entryType.displayName),
              if (entry.referenceNumber != null)
                _buildDetailRow('Reference', entry.referenceNumber!),
              const SizedBox(height: 16),
              Text(
                'Journal Lines',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              ...entry.lines.map((line) => Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${line.accountNumber} - ${line.accountName}',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(line.description),
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Debit: \$${line.debitAmount.toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: line.debitAmount > 0
                                      ? Colors.green
                                      : Colors.grey,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                'Credit: \$${line.creditAmount.toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: line.creditAmount > 0
                                      ? Colors.red
                                      : Colors.grey,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  )),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Debits: \$${entry.totalDebits.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  Text(
                    'Total Credits: \$${entry.totalCredits.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _confirmPostEntry(BuildContext context, JournalEntryModel entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Post Journal Entry'),
        content: Text(
          'Are you sure you want to post journal entry ${entry.entryNumber}? '
          'Posted entries cannot be modified.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.find<JournalEntryController>().postJournalEntry(entry.id);
            },
            child: const Text('Post'),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteEntry(BuildContext context, JournalEntryModel entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Journal Entry'),
        content: Text(
          'Are you sure you want to delete journal entry ${entry.entryNumber}? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.find<JournalEntryController>().deleteJournalEntry(entry.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
