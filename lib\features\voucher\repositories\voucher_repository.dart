import 'dart:developer';
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/firebase_service/voucher/voucher_crud_firebase_service.dart';
import 'package:logestics/firebase_service/finance/deposit_firebase_service.dart';
import 'package:logestics/firebase_service/finance/expense_firebase_service.dart';
import 'package:logestics/firebase_service/finance/account_firebase_service.dart';
import 'package:logestics/firebase_service/finance/fuel_card_firebase_service.dart';
import 'package:logestics/firebase_service/finance/check_usage_firebase_service.dart';
import 'package:logestics/features/finance/deposits/repository/deposit_repository.dart';
import 'package:logestics/features/finance/expenses/repositories/expense_repository.dart';
import 'package:logestics/features/finance/fuel_cards/repositories/fuel_card_repository.dart';
import 'package:logestics/features/finance/check_usage/repositories/check_usage_repository.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/core/services/voucher_accounting_hook_service.dart';
import 'package:logestics/core/services/voucher_chart_of_accounts_service.dart';
import 'package:logestics/models/voucher_model.dart';
import 'package:logestics/models/payment_transaction_model.dart';

abstract class VoucherRepository {
  /// Creates an invoice and returns either a success or error object.
  Future<Either<FailureObj, SuccessObj>> createVoucher({
    required String uid,
    required Map<String, dynamic> voucher,
  });
  Future<Either<FailureObj, SuccessObj>> updateVoucher({
    required String uid,
    required Map<String, dynamic> voucher,
  });
  Stream<List<DocumentChange>> listenToVoucher({required String uid});

  Future<Either<FailureObj, SuccessObj>> deleteVoucher({
    required String uid,
    required String voucherNumber,
  });
}

class VoucherRepositoryImp implements VoucherRepository {
  final VoucherCrudFirebaseService _voucherCrudFirebaseService;
  final VoucherAccountingHookService _hookService;

  VoucherRepositoryImp(this._voucherCrudFirebaseService)
      : _hookService = VoucherAccountingHookService();

  /// Creates an invoice and returns either a success or error object.
  @override
  Future<Either<FailureObj, SuccessObj>> createVoucher({
    required String uid,
    required Map<String, dynamic> voucher,
  }) async {
    try {
      // Validate Chart of Accounts fields if present
      final validationResult = _validateChartOfAccountsFields(voucher);
      if (!validationResult.isValid) {
        return Left(FailureObj(
          code: 'validation-error',
          message:
              'Chart of Accounts validation failed: ${validationResult.errors.join(', ')}',
        ));
      }

      await _voucherCrudFirebaseService.createVoucherToFirebase(
        uid: uid,
        voucher: voucher,
      );

      // Trigger accounting hook for journal entry generation
      await _hookService.onVoucherCreated(voucher, uid);

      return Right(SuccessObj(message: 'Voucher created successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> updateVoucher({
    required String uid,
    required Map<String, dynamic> voucher,
  }) async {
    try {
      // Validate Chart of Accounts fields if present
      final validationResult = _validateChartOfAccountsFields(voucher);
      if (!validationResult.isValid) {
        return Left(FailureObj(
          code: 'validation-error',
          message:
              'Chart of Accounts validation failed: ${validationResult.errors.join(', ')}',
        ));
      }

      // Get the old voucher data first for hook service
      final voucherNumber = voucher['voucherNumber'] as String? ?? '';
      final oldVoucherData =
          await _voucherCrudFirebaseService.getVoucherByNumber(
        uid: uid,
        voucherNumber: voucherNumber,
      );

      await _voucherCrudFirebaseService.updateVoucherToFirebase(
        uid: uid,
        voucher: voucher,
      );

      // Trigger accounting hook for voucher update
      if (oldVoucherData != null) {
        await _hookService.onVoucherUpdated(oldVoucherData, voucher, uid);
      }

      return Right(SuccessObj(message: 'Voucher updated successfully.'));
    } on SocketException {
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred: $e',
      ));
    }
  }

  @override
  Stream<List<DocumentChange>> listenToVoucher({required String uid}) {
    return _voucherCrudFirebaseService.listenToVouchers(uid: uid);
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteVoucher({
    required String uid,
    required String voucherNumber,
  }) async {
    log('🗑️ Starting voucher deletion process for voucher: $voucherNumber (UID: $uid)');

    try {
      // Get voucher data before deletion for hook service
      log('📋 Phase 0: Getting voucher data for accounting hook...');
      final voucherData = await _voucherCrudFirebaseService.getVoucherByNumber(
        uid: uid,
        voucherNumber: voucherNumber,
      );

      // First, clean up all financial entries associated with this voucher
      log('🧹 Phase 1: Starting financial cleanup...');
      await _cleanupVoucherFinancialEntries(voucherNumber);
      log('✅ Phase 1 completed: Financial cleanup successful');

      // Trigger accounting hook for voucher deletion (before actual deletion)
      if (voucherData != null) {
        log('📊 Phase 1.5: Triggering accounting hook for journal entry reversal...');
        await _hookService.onVoucherDeleted(voucherData, uid);
        log('✅ Phase 1.5 completed: Accounting hook processed');
      }

      // Then delete the voucher document itself
      log('📄 Phase 2: Deleting voucher document...');
      await _voucherCrudFirebaseService.deleteVoucherFromFirebase(
        uid: uid,
        voucherNumber: voucherNumber,
      );
      log('✅ Phase 2 completed: Voucher document deleted successfully');

      log('🎉 Voucher deletion completed successfully for voucher: $voucherNumber');
      return Right(SuccessObj(
          message:
              'Voucher and all associated financial records deleted successfully.'));
    } on SocketException catch (e) {
      log('🌐 Network error during voucher deletion: $e');
      return Left(FailureObj(
        code: 'no-internet',
        message: 'No internet connection. Please check your network.',
      ));
    } on ArgumentError catch (e) {
      log('⚠️ Argument error during voucher deletion: $e');
      return Left(FailureObj(
        code: 'invalid-argument',
        message: e.message ?? 'Invalid argument provided.',
      ));
    } on FirebaseException catch (e) {
      log('🔥 Firebase error during voucher deletion: $e');
      return Left(FailureObj(
        code: e.code,
        message: e.message ?? 'Firebase error occurred.',
      ));
    } catch (e) {
      log('💥 Unexpected error during voucher deletion: $e');
      return Left(FailureObj(
        code: 'unexpected-error',
        message: 'An unexpected error occurred during voucher deletion: $e',
      ));
    }
  }

  /// Comprehensive cleanup of all financial entries associated with a voucher
  Future<void> _cleanupVoucherFinancialEntries(String voucherNumber) async {
    log('🚀 Starting comprehensive financial cleanup for voucher: $voucherNumber');

    try {
      // Create reference number by prefixing 'V-' to the voucher number
      final referenceNumber = 'V-$voucherNumber';
      log('🔍 Using reference number for cleanup: $referenceNumber');

      // Initialize repositories
      log('🏗️ Initializing financial repositories...');
      final depositRepository = DepositRepositoryImpl(DepositFirebaseService());
      final expenseRepository = ExpenseRepositoryImpl(
        ExpenseFirebaseService(),
        AccountFirebaseService(),
      );
      final fuelCardRepository =
          FuelCardRepositoryImpl(FuelCardFirebaseService());
      final checkUsageRepository =
          CheckUsageRepository(CheckUsageFirebaseService());

      log('✅ Repositories initialized successfully');

      // 1. Clean up all deposit entries (broker fees, munshiana, company freight, sales tax, tax authority payments, net profit)
      log('📥 Step 1: Cleaning up deposit entries...');
      await _cleanupDepositEntries(depositRepository, referenceNumber);

      // 1.1. Additional verification for tax authority deposits
      log('🏛️ Step 1.1: Verifying tax authority deposit cleanup...');
      await _verifyTaxAuthorityCleanup(depositRepository, referenceNumber);

      // 2. Clean up all expense entries (outsource broker fees, net loss)
      log('📤 Step 2: Cleaning up expense entries...');
      await _cleanupExpenseEntries(expenseRepository, referenceNumber);

      // 3. Clean up fuel card usage records
      log('⛽ Step 3: Cleaning up fuel card usage records...');
      await _cleanupFuelCardUsage(fuelCardRepository, voucherNumber);

      // 4. Clean up check usage records
      log('💳 Step 4: Cleaning up check usage records...');
      await _cleanupCheckUsage(checkUsageRepository, voucherNumber);

      log('🎉 Successfully completed comprehensive financial cleanup for voucher: $voucherNumber');
    } catch (e) {
      log('💥 Error during financial cleanup for voucher $voucherNumber: $e');
      throw Exception('Failed to cleanup financial entries: $e');
    }
  }

  /// Clean up all deposit entries associated with the voucher
  Future<void> _cleanupDepositEntries(
      DepositRepository depositRepository, String referenceNumber) async {
    log('🧹 Starting deposit cleanup for reference: $referenceNumber');

    try {
      final depositsResult = await depositRepository.getDeposits();

      depositsResult.fold(
        (failure) {
          log('❌ Failed to fetch deposits for cleanup: ${failure.message}');
          throw Exception('Failed to fetch deposits: ${failure.message}');
        },
        (deposits) async {
          log('📊 Total deposits in system: ${deposits.length}');

          // Find all deposits with matching reference number
          final voucherDeposits = deposits
              .where((deposit) => deposit.referenceNumber == referenceNumber)
              .toList();

          log('🎯 Found ${voucherDeposits.length} deposit entries to clean up for reference: $referenceNumber');

          if (voucherDeposits.isEmpty) {
            log('⚠️ No deposit entries found for reference: $referenceNumber');
            return;
          }

          // Log details of deposits to be deleted
          for (final deposit in voucherDeposits) {
            log('📝 Deposit to delete: ID=${deposit.id}, Account=${deposit.accountName}, Amount=${deposit.amount}, Category=${deposit.categoryId}, PayerName=${deposit.payerName}, Notes=${deposit.notes}');
          }

          // Special logging for tax authority deposits
          final taxAuthorityDeposits = voucherDeposits
              .where((deposit) =>
                  deposit.categoryId == 'tax_authority_single' ||
                  deposit.categoryId == 'tax_authority_split')
              .toList();

          if (taxAuthorityDeposits.isNotEmpty) {
            log('🏛️ Found ${taxAuthorityDeposits.length} tax authority deposits to delete:');
            for (final taxDeposit in taxAuthorityDeposits) {
              log('   💰 Tax Deposit: ${taxDeposit.categoryId} - ${taxDeposit.payerName} - \$${taxDeposit.amount}');
            }
          }

          // Calculate total amount to be removed for verification
          final totalAmountToRemove = voucherDeposits.fold(
              0.0, (total, deposit) => total + deposit.amount);
          log('💰 Total amount to be removed from accounts: \$${totalAmountToRemove.toStringAsFixed(2)}');

          // Group deposits by account for batch processing
          final depositsByAccount = <String, List<DepositModel>>{};
          for (final deposit in voucherDeposits) {
            depositsByAccount
                .putIfAbsent(deposit.accountId, () => [])
                .add(deposit);
          }

          log('📊 Deposits grouped by ${depositsByAccount.length} accounts');

          // Delete each deposit entry
          for (final deposit in voucherDeposits) {
            try {
              log('🗑️ Deleting deposit: ${deposit.id} - ${deposit.categoryId} - ${deposit.payerName} - \$${deposit.amount}');
              log('   📍 Account: ${deposit.accountName} (${deposit.accountId})');
              log('   📄 Reference: ${deposit.referenceNumber}');

              await depositRepository.deleteDeposit(
                deposit.id,
                deposit.accountId,
                deposit.amount,
              );

              log('✅ Successfully deleted deposit: ${deposit.id} - \$${deposit.amount} removed from ${deposit.accountName}');
            } catch (e) {
              log('❌ Failed to delete deposit ${deposit.id}: $e');
              throw Exception('Failed to delete deposit ${deposit.id}: $e');
            }
          }

          // Verify all deposits were deleted
          log('🎯 Verification: All ${voucherDeposits.length} deposits processed for reference: $referenceNumber');

          log('🎉 Successfully cleaned up ${voucherDeposits.length} deposit entries');
        },
      );
    } catch (e) {
      log('💥 Error cleaning up deposit entries: $e');
      throw Exception('Failed to cleanup deposit entries: $e');
    }
  }

  /// Clean up all expense entries associated with the voucher
  Future<void> _cleanupExpenseEntries(
      ExpenseRepository expenseRepository, String referenceNumber) async {
    log('🧹 Starting expense cleanup for reference: $referenceNumber');

    try {
      final expensesResult = await expenseRepository.getExpenses();

      expensesResult.fold(
        (failure) {
          log('❌ Failed to fetch expenses for cleanup: ${failure.message}');
          throw Exception('Failed to fetch expenses: ${failure.message}');
        },
        (expenses) async {
          log('📊 Total expenses in system: ${expenses.length}');

          // Find all expenses with matching reference number
          final voucherExpenses = expenses
              .where((expense) => expense.referenceNumber == referenceNumber)
              .toList();

          log('🎯 Found ${voucherExpenses.length} expense entries to clean up for reference: $referenceNumber');

          if (voucherExpenses.isEmpty) {
            log('⚠️ No expense entries found for reference: $referenceNumber');
            return;
          }

          // Log details of expenses to be deleted
          for (final expense in voucherExpenses) {
            log('📝 Expense to delete: ID=${expense.id}, Account=${expense.accountName}, Amount=${expense.amount}, Category=${expense.categoryId}, Title=${expense.title}');
          }

          // Delete each expense entry
          for (final expense in voucherExpenses) {
            try {
              log('🗑️ Deleting expense: ${expense.id} - ${expense.title}');
              await expenseRepository.deleteExpense(
                expense.id,
                expense.accountId,
                expense.amount,
              );
              log('✅ Successfully deleted expense: ${expense.id}');
            } catch (e) {
              log('❌ Failed to delete expense ${expense.id}: $e');
              throw Exception('Failed to delete expense ${expense.id}: $e');
            }
          }

          log('🎉 Successfully cleaned up ${voucherExpenses.length} expense entries');
        },
      );
    } catch (e) {
      log('💥 Error cleaning up expense entries: $e');
      throw Exception('Failed to cleanup expense entries: $e');
    }
  }

  /// Clean up fuel card usage records associated with the voucher
  Future<void> _cleanupFuelCardUsage(
      FuelCardRepository fuelCardRepository, String voucherNumber) async {
    log('⛽ Starting fuel card usage cleanup for voucher: $voucherNumber');

    try {
      // Note: FuelCardRepository doesn't have a method to get fuel card usage records
      // This would need to be implemented in the fuel card service/repository
      // For now, we'll log that this cleanup is needed
      log('⚠️ Fuel card usage cleanup for voucher $voucherNumber - implementation needed in FuelCardRepository');
      log('📝 Need to add getFuelCardUsageByVoucher() method to FuelCardRepository');
    } catch (e) {
      log('💥 Error cleaning up fuel card usage: $e');
      throw Exception('Failed to cleanup fuel card usage: $e');
    }
  }

  /// Clean up check usage records associated with the voucher
  Future<void> _cleanupCheckUsage(
      CheckUsageRepository checkUsageRepository, String voucherNumber) async {
    log('💳 Starting check usage cleanup for voucher: $voucherNumber');

    try {
      final checkUsagesResult = await checkUsageRepository.getCheckUsages();

      checkUsagesResult.fold(
        (failure) {
          log('❌ Failed to fetch check usages for cleanup: ${failure.message}');
          throw Exception('Failed to fetch check usages: ${failure.message}');
        },
        (checkUsages) async {
          log('📊 Total check usages in system: ${checkUsages.length}');

          // Find all check usages with matching voucher number
          final voucherCheckUsages = checkUsages
              .where((checkUsage) => checkUsage.voucherNumber == voucherNumber)
              .toList();

          log('🎯 Found ${voucherCheckUsages.length} check usage entries to clean up for voucher: $voucherNumber');

          if (voucherCheckUsages.isEmpty) {
            log('⚠️ No check usage entries found for voucher: $voucherNumber');
            return;
          }

          // Log details of check usages to be deleted
          for (final checkUsage in voucherCheckUsages) {
            log('📝 Check usage to delete: ID=${checkUsage.id}, CheckNumber=${checkUsage.checkNumber}, Amount=${checkUsage.amount}, PayeeName=${checkUsage.payeeName}');
          }

          // Delete each check usage entry
          for (final checkUsage in voucherCheckUsages) {
            try {
              log('🗑️ Deleting check usage: ${checkUsage.id} - Check #${checkUsage.checkNumber}');
              await checkUsageRepository.deleteCheckUsage(checkUsage.id);
              log('✅ Successfully deleted check usage: ${checkUsage.id}');
            } catch (e) {
              log('❌ Failed to delete check usage ${checkUsage.id}: $e');
              throw Exception(
                  'Failed to delete check usage ${checkUsage.id}: $e');
            }
          }

          log('🎉 Successfully cleaned up ${voucherCheckUsages.length} check usage entries');
        },
      );
    } catch (e) {
      log('💥 Error cleaning up check usage: $e');
      throw Exception('Failed to cleanup check usage: $e');
    }
  }

  /// Verify that all tax authority deposits have been properly cleaned up
  Future<void> _verifyTaxAuthorityCleanup(
      DepositRepository depositRepository, String referenceNumber) async {
    log('🔍 Verifying tax authority deposit cleanup for reference: $referenceNumber');

    try {
      final depositsResult = await depositRepository.getDeposits();

      depositsResult.fold(
        (failure) {
          log('❌ Failed to fetch deposits for verification: ${failure.message}');
          throw Exception(
              'Failed to fetch deposits for verification: ${failure.message}');
        },
        (deposits) async {
          // Find any remaining tax authority deposits for this voucher
          final remainingTaxDeposits = deposits
              .where((deposit) =>
                  deposit.referenceNumber == referenceNumber &&
                  (deposit.categoryId == 'tax_authority_single' ||
                      deposit.categoryId == 'tax_authority_split'))
              .toList();

          if (remainingTaxDeposits.isNotEmpty) {
            log('⚠️ WARNING: Found ${remainingTaxDeposits.length} remaining tax authority deposits!');
            for (final deposit in remainingTaxDeposits) {
              log('   🚨 Remaining: ${deposit.categoryId} - ${deposit.payerName} - \$${deposit.amount}');
              log('      ID: ${deposit.id}, Account: ${deposit.accountName}');
            }

            // Attempt to clean up the remaining deposits
            log('🧹 Attempting to clean up remaining tax authority deposits...');
            for (final deposit in remainingTaxDeposits) {
              try {
                log('🗑️ Force deleting remaining tax deposit: ${deposit.id}');
                await depositRepository.deleteDeposit(
                  deposit.id,
                  deposit.accountId,
                  deposit.amount,
                );
                log('✅ Successfully force deleted tax deposit: ${deposit.id}');
              } catch (e) {
                log('❌ Failed to force delete tax deposit ${deposit.id}: $e');
                throw Exception(
                    'Failed to force delete remaining tax deposit: $e');
              }
            }
          } else {
            log('✅ Verification passed: No remaining tax authority deposits found');
          }
        },
      );
    } catch (e) {
      log('💥 Error during tax authority cleanup verification: $e');
      throw Exception('Failed to verify tax authority cleanup: $e');
    }
  }

  /// Validate Chart of Accounts fields in voucher data
  ValidationResult _validateChartOfAccountsFields(
      Map<String, dynamic> voucherData) {
    try {
      // Convert to VoucherModel for validation
      final voucher = VoucherModel.fromJson(voucherData);

      // Use the existing Chart of Accounts validation service
      return VoucherChartOfAccountsService.validateVoucherAccounts(
        brokerAccount: null, // We don't have the actual account objects here
        munshianaAccount: null,
        salesTaxAccount: null,
        freightTaxAccount: null,
        profitAccount: null,
        paymentTransactions: voucher.paymentTransactions
            .map((pt) => PaymentTransactionModel.fromMap(pt))
            .toList(),
      );
    } catch (e) {
      log('Error validating Chart of Accounts fields: $e');
      return ValidationResult(
        isValid: false,
        errors: ['Failed to validate Chart of Accounts fields: $e'],
        warnings: [],
      );
    }
  }
}
