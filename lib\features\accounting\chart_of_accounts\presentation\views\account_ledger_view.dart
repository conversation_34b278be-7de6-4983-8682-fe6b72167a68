import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import '../../../../../core/utils/app_text_styles.dart';
import '../../../../../features/home/<USER>/theme.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../controllers/account_ledger_controller.dart';

/// Enhanced account ledger view showing individual transaction entries with running balances
class AccountLedgerView extends StatefulWidget {
  final ChartOfAccountsModel account;
  final String uid;

  const AccountLedgerView({
    super.key,
    required this.account,
    required this.uid,
  });

  @override
  State<AccountLedgerView> createState() => _AccountLedgerViewState();
}

class _AccountLedgerViewState extends State<AccountLedgerView> {
  late AccountLedgerController controller;
  DateTimeRange? selectedDateRange;

  @override
  void initState() {
    super.initState();
    controller = Get.put(AccountLedgerController());
    // Load ledger entries for this account
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadLedgerEntriesForAccount(widget.account.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    final notifier = Provider.of<ColorNotifier>(context, listen: true);

    return Scaffold(
      backgroundColor: notifier.getBgColor,
      appBar: AppBar(
        title: Text('${widget.account.accountName} - Ledger'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshLedgerEntries(),
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: () => controller.exportLedgerToExcel(widget.account),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildHeader(notifier),
          _buildFilters(notifier),
          Expanded(child: _buildLedgerEntriesList(notifier)),
          _buildPagination(notifier),
        ],
      ),
    );
  }

  Widget _buildHeader(ColorNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.account.category.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.account_balance_wallet,
                  color: widget.account.category.color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.account.accountName,
                      style: AppTextStyles.headingLarge.copyWith(
                        color: notifier.text,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.account.accountNumber} • ${widget.account.accountType.displayName}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Obx(() => Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: controller.currentBalance.value >= 0
                          ? Colors.green[50]
                          : Colors.red[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: controller.currentBalance.value >= 0
                            ? Colors.green[200]!
                            : Colors.red[200]!,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Current Balance',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          '₨ ${controller.currentBalance.value.toStringAsFixed(2)}',
                          style: AppTextStyles.headingMedium.copyWith(
                            color: controller.currentBalance.value >= 0
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilters(ColorNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search transactions...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) => controller.setSearchQuery(value),
            ),
          ),
          const SizedBox(width: 16),
          OutlinedButton.icon(
            onPressed: () => _selectDateRange(),
            icon: const Icon(Icons.date_range),
            label: Text(selectedDateRange == null
                ? 'Date Range'
                : '${_formatDate(selectedDateRange!.start)} - ${_formatDate(selectedDateRange!.end)}'),
          ),
          if (selectedDateRange != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: () {
                setState(() {
                  selectedDateRange = null;
                });
                controller.clearDateFilter();
              },
              icon: const Icon(Icons.clear),
              tooltip: 'Clear date filter',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLedgerEntriesList(ColorNotifier notifier) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.isError.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                'Error loading ledger entries',
                style: AppTextStyles.headingMedium,
              ),
              const SizedBox(height: 8),
              Text(
                controller.errorMessage.value,
                style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => controller.refreshLedgerEntries(),
                child: const Text('Retry'),
              ),
            ],
          ),
        );
      }

      if (!controller.hasLedgerEntries) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.receipt_long, size: 64, color: Colors.grey[300]),
              const SizedBox(height: 16),
              Text(
                'No transactions found',
                style: AppTextStyles.headingMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'This account has no ledger entries yet.',
                style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return _buildLedgerEntriesTable(notifier);
    });
  }

  Widget _buildLedgerEntriesTable(ColorNotifier notifier) {
    final currentEntries = controller.getCurrentPageLedgerEntries();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        headingRowHeight: 56,
        columnSpacing: 16,
        headingTextStyle: TextStyle(
          color: notifier.text,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        dataTextStyle: TextStyle(
          color: notifier.text,
          fontSize: 13,
        ),
        headingRowColor: WidgetStateProperty.resolveWith<Color?>(
          (Set<WidgetState> states) {
            return notifier.getBgColor.withValues(alpha: 0.1);
          },
        ),
        columns: const [
          DataColumn(label: Text('Date')),
          DataColumn(label: Text('Reference')),
          DataColumn(label: Text('Description')),
          DataColumn(label: Text('Debit')),
          DataColumn(label: Text('Credit')),
          DataColumn(label: Text('Balance')),
        ],
        rows: currentEntries.map((entry) {
          return DataRow(
            cells: [
              DataCell(Text(controller.formatDate(entry.transactionDate))),
              DataCell(
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getReferenceTypeColor(entry.referenceType).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    entry.formattedReference,
                    style: TextStyle(
                      color: _getReferenceTypeColor(entry.referenceType),
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
              DataCell(
                SizedBox(
                  width: 200,
                  child: Text(
                    entry.description,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ),
              DataCell(
                Text(
                  entry.debitAmount > 0 ? '₨ ${entry.debitAmount.toStringAsFixed(2)}' : '-',
                  style: TextStyle(
                    color: entry.debitAmount > 0 ? Colors.green[700] : Colors.grey[500],
                    fontWeight: entry.debitAmount > 0 ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
              DataCell(
                Text(
                  entry.creditAmount > 0 ? '₨ ${entry.creditAmount.toStringAsFixed(2)}' : '-',
                  style: TextStyle(
                    color: entry.creditAmount > 0 ? Colors.red[700] : Colors.grey[500],
                    fontWeight: entry.creditAmount > 0 ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
              DataCell(
                Text(
                  '₨ ${entry.runningBalance.toStringAsFixed(2)}',
                  style: TextStyle(
                    color: entry.runningBalance >= 0 ? Colors.blue[700] : Colors.red[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPagination(ColorNotifier notifier) {
    return Obx(() {
      if (!controller.hasLedgerEntries) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: notifier.getBgColor,
          border: Border(
            top: BorderSide(color: Colors.grey[300]!),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Showing ${controller.currentPageStart} - ${controller.currentPageEnd} of ${controller.totalLedgerEntries} entries',
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
            ),
            Row(
              children: [
                IconButton(
                  onPressed: controller.canGoPreviousPage ? () => controller.previousPage() : null,
                  icon: const Icon(Icons.chevron_left),
                ),
                Text(
                  'Page ${controller.currentPage} of ${controller.totalPages}',
                  style: AppTextStyles.bodyMedium,
                ),
                IconButton(
                  onPressed: controller.canGoNextPage ? () => controller.nextPage() : null,
                  icon: const Icon(Icons.chevron_right),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Color _getReferenceTypeColor(String referenceType) {
    switch (referenceType.toLowerCase()) {
      case 'voucher':
        return Colors.blue[700]!;
      case 'invoice':
        return Colors.green[700]!;
      case 'expense':
        return Colors.red[700]!;
      case 'deposit':
        return Colors.teal[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        selectedDateRange = picked;
      });
      controller.setDateFilter(picked.start, picked.end);
    }
  }
}
